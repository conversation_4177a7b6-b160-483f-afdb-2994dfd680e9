#!/usr/bin/env python3
"""
Comprehensive C2 system test - tests actual client-server communication
"""

import subprocess
import time
import signal
import sys
import os
import socket
import threading
import json
import re
from pathlib import Path

class ComprehensiveC2Test:
    def __init__(self):
        self.server_process = None
        self.client_process = None
        self.test_results = []
        self.server_output = []
        self.client_output = []
        
    def log_result(self, test_name, success, details=""):
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append((test_name, success, details))
        print(f"{status}: {test_name}")
        if details:
            print(f"    {details}")
    
    def monitor_process_output(self, process, output_list, name):
        """Monitor process output in real-time"""
        try:
            while process.poll() is None:
                line = process.stdout.readline()
                if line:
                    output_list.append(line.strip())
                    print(f"[{name}] {line.strip()}")
        except:
            pass
    
    def start_server(self):
        """Start C2 server with monitoring"""
        print("🚀 Starting C2 Server...")
        try:
            self.server_process = subprocess.Popen([
                sys.executable, "c2_server.py", "8080", "--standard-ports", "--verbose"
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
            
            # Start monitoring thread
            server_monitor = threading.Thread(
                target=self.monitor_process_output, 
                args=(self.server_process, self.server_output, "SERVER")
            )
            server_monitor.daemon = True
            server_monitor.start()
            
            # Give server time to start
            time.sleep(8)
            
            # Check if server is running
            if self.server_process.poll() is None:
                self.log_result("Server Startup", True, "Server started and running")
                return True
            else:
                self.log_result("Server Startup", False, "Server process terminated")
                return False
                
        except Exception as e:
            self.log_result("Server Startup", False, f"Exception: {e}")
            return False
    
    def start_client(self):
        """Start C2 client with monitoring"""
        print("🚀 Starting C2 Client...")
        try:
            self.client_process = subprocess.Popen([
                sys.executable, "client.py", "127.0.0.1"
            ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, bufsize=1)
            
            # Start monitoring thread
            client_monitor = threading.Thread(
                target=self.monitor_process_output, 
                args=(self.client_process, self.client_output, "CLIENT")
            )
            client_monitor.daemon = True
            client_monitor.start()
            
            # Give client time to connect
            time.sleep(15)
            
            # Check if client is running
            if self.client_process.poll() is None:
                self.log_result("Client Startup", True, "Client started and running")
                return True
            else:
                self.log_result("Client Startup", False, "Client process terminated")
                return False
                
        except Exception as e:
            self.log_result("Client Startup", False, f"Exception: {e}")
            return False
    
    def test_client_connection(self):
        """Test if client successfully connected to server"""
        print("🔗 Testing client-server connection...")
        
        # Wait for connection to establish
        time.sleep(10)
        
        # Check server output for client connection
        server_text = "\n".join(self.server_output)
        client_text = "\n".join(self.client_output)
        
        # Look for connection indicators
        connection_indicators = [
            "TLS handshake completed",
            "Client certificate verified",
            "New client connected",
            "Application layer security initialized"
        ]
        
        found_indicators = []
        for indicator in connection_indicators:
            if indicator.lower() in server_text.lower():
                found_indicators.append(indicator)
        
        if found_indicators:
            self.log_result("Client Connection", True, f"Found indicators: {found_indicators}")
        else:
            self.log_result("Client Connection", False, "No connection indicators found")
            print("Server output:", server_text[-500:])  # Last 500 chars
            print("Client output:", client_text[-500:])   # Last 500 chars
    
    def test_tls_handshake(self):
        """Test TLS handshake completion"""
        print("🔒 Testing TLS handshake...")
        
        server_text = "\n".join(self.server_output)
        client_text = "\n".join(self.client_output)
        
        # Look for TLS handshake success
        tls_indicators = [
            "TLS handshake completed",
            "mutual TLS",
            "certificate loaded",
            "certificate verified"
        ]
        
        found_tls = []
        for indicator in tls_indicators:
            if indicator.lower() in (server_text + client_text).lower():
                found_tls.append(indicator)
        
        if found_tls:
            self.log_result("TLS Handshake", True, f"TLS indicators: {found_tls}")
        else:
            self.log_result("TLS Handshake", False, "No TLS handshake indicators found")
    
    def test_certificate_bootstrap(self):
        """Test certificate bootstrap process"""
        print("🔐 Testing certificate bootstrap...")
        
        client_text = "\n".join(self.client_output)
        
        # Look for bootstrap indicators
        bootstrap_indicators = [
            "secure bootstrap",
            "CA certificate",
            "certificate generated",
            "certificate loaded"
        ]
        
        found_bootstrap = []
        for indicator in bootstrap_indicators:
            if indicator.lower() in client_text.lower():
                found_bootstrap.append(indicator)
        
        if found_bootstrap:
            self.log_result("Certificate Bootstrap", True, f"Bootstrap indicators: {found_bootstrap}")
        else:
            self.log_result("Certificate Bootstrap", False, "No bootstrap indicators found")
    
    def test_secure_communication(self):
        """Test secure communication establishment"""
        print("🛡️ Testing secure communication...")
        
        combined_text = "\n".join(self.server_output + self.client_output)
        
        # Look for security indicators
        security_indicators = [
            "application layer security",
            "session key",
            "encrypted",
            "secure message"
        ]
        
        found_security = []
        for indicator in security_indicators:
            if indicator.lower() in combined_text.lower():
                found_security.append(indicator)
        
        if found_security:
            self.log_result("Secure Communication", True, f"Security indicators: {found_security}")
        else:
            self.log_result("Secure Communication", False, "No security indicators found")
    
    def test_no_insecure_fallbacks(self):
        """Test that no insecure fallbacks were used"""
        print("🚫 Testing no insecure fallbacks...")
        
        combined_text = "\n".join(self.server_output + self.client_output)
        
        # Look for insecure fallback indicators (these should NOT be present)
        insecure_indicators = [
            "CERT_NONE",
            "connecting without server verification",
            "skipping certificate verification",
            "no CA certificate - connecting",
            "insecure fallback"
        ]
        
        found_insecure = []
        for indicator in insecure_indicators:
            if indicator.lower() in combined_text.lower():
                found_insecure.append(indicator)
        
        if not found_insecure:
            self.log_result("No Insecure Fallbacks", True, "No insecure fallbacks detected")
        else:
            self.log_result("No Insecure Fallbacks", False, f"Insecure fallbacks found: {found_insecure}")
    
    def cleanup(self):
        """Clean up processes"""
        print("🧹 Cleaning up...")
        
        if self.client_process:
            try:
                self.client_process.terminate()
                self.client_process.wait(timeout=5)
            except:
                self.client_process.kill()
        
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
            except:
                self.server_process.kill()
    
    def run_comprehensive_test(self):
        """Run comprehensive system test"""
        print("🧪 Starting Comprehensive C2 System Test")
        print("=" * 70)
        
        try:
            # Test 1: Start server
            if not self.start_server():
                print("❌ Server startup failed - aborting tests")
                return False
            
            # Test 2: Start client
            if not self.start_client():
                print("❌ Client startup failed - continuing with available tests")
            
            # Wait for connection to establish
            print("⏳ Waiting for connection to establish...")
            time.sleep(20)
            
            # Test 3: Test client connection
            self.test_client_connection()
            
            # Test 4: Test TLS handshake
            self.test_tls_handshake()
            
            # Test 5: Test certificate bootstrap
            self.test_certificate_bootstrap()
            
            # Test 6: Test secure communication
            self.test_secure_communication()
            
            # Test 7: Test no insecure fallbacks
            self.test_no_insecure_fallbacks()
            
            # Wait a bit more for any final logs
            time.sleep(5)
            
        finally:
            self.cleanup()
        
        # Print results
        print("\n" + "=" * 70)
        print("🧪 Comprehensive Test Results")
        print("=" * 70)
        
        passed = 0
        failed = 0
        
        for test_name, success, details in self.test_results:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status}: {test_name}")
            if details:
                print(f"    {details}")
            
            if success:
                passed += 1
            else:
                failed += 1
        
        print("=" * 70)
        print(f"Total: {passed + failed} tests")
        print(f"✅ Passed: {passed}")
        print(f"❌ Failed: {failed}")
        
        if failed == 0:
            print("🎉 ALL COMPREHENSIVE TESTS PASSED!")
            print("🔒 Secure TLS implementation is working correctly!")
            return True
        else:
            print("💥 SOME TESTS FAILED!")
            return False

def main():
    """Run the comprehensive system test"""
    test = ComprehensiveC2Test()
    success = test.run_comprehensive_test()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
