#!/usr/bin/env python3

"""
Checkpoint 11: Pre-generate DH parameters for better performance
This script generates safe DH parameters and saves them for reuse
"""

import os
import json
from cryptography.hazmat.primitives.asymmetric import dh
from cryptography.hazmat.primitives import serialization
from secure_storage import SecureStorage

def generate_dh_parameters():
    """Generate safe DH parameters"""
    print("🔐 Generating DH parameters (this may take a moment)...")
    
    # Generate 2048-bit DH parameters
    parameters = dh.generate_parameters(generator=2, key_size=2048)
    
    # Serialize parameters
    pem_params = parameters.parameter_bytes(
        encoding=serialization.Encoding.PEM,
        format=serialization.ParameterFormat.PKCS3
    )
    
    # Also get the raw p and g values for compatibility
    numbers = parameters.parameter_numbers()
    
    params_data = {
        'pem': pem_params.decode('utf-8'),
        'p': str(numbers.p),
        'g': str(numbers.g)
    }
    
    return params_data

def save_dh_parameters(params_data, filename='dh_params.json'):
    """Save DH parameters to secure storage"""
    storage = SecureStorage()
    params_json = json.dumps(params_data, indent=2)
    storage.store_secret_file(filename, params_json, is_binary=False)
    print(f"✅ DH parameters saved to secure storage: {filename}")

def main():
    print("🎪 Elissa's Fun House - DH Parameter Generator")
    print("=" * 50)
    
    # Check if parameters already exist in secure storage
    storage = SecureStorage()
    dh_params_path = storage.get_secret_path('dh_params.json')
    if dh_params_path.exists():
        response = input("DH parameters already exist. Regenerate? (y/N): ")
        if not response.lower().startswith('y'):
            print("Using existing parameters.")
            return
    
    # Generate and save parameters
    params_data = generate_dh_parameters()
    save_dh_parameters(params_data)
    
    print("\n🔒 Security Info:")
    print(f"   P (prime): {len(params_data['p'])} digits")
    print(f"   G (generator): {params_data['g']}")
    print("\n✅ DH parameters ready for use!")

if __name__ == "__main__":
    main()
