#!/usr/bin/env python3

import os
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

def encrypt_onion_address():
    """Get the encrypted onion address for client embedding"""
    
    # Read the onion address from the hidden service
    hostname_file = "/home/<USER>/.tor_c2/hidden_service/hostname"
    
    if not os.path.exists(hostname_file):
        print("❌ Hidden service hostname file not found")
        return None
    
    with open(hostname_file, 'r') as f:
        onion_address = f.read().strip()
    
    print(f"🧅 Onion address: {onion_address}")
    
    # Use the same master key as the C2 system
    password = os.getenv('C2_MASTER_PASSWORD', "ElissasFunHouse2024SecureKey!@#$%^&*()").encode('utf-8')
    salt = os.getenv('C2_MASTER_SALT', "SecureSalt123456").encode('utf-8')
    
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    master_key = kdf.derive(password)
    master_key_b64 = base64.urlsafe_b64encode(master_key)
    cipher = Fernet(master_key_b64)
    
    # Encrypt the onion address
    encrypted_onion = cipher.encrypt(onion_address.encode('utf-8'))
    encrypted_onion_b64 = base64.b64encode(encrypted_onion).decode('ascii')
    
    print(f"🔐 Encrypted onion address for client:")
    print(f'"{encrypted_onion_b64}"')
    
    return encrypted_onion_b64

if __name__ == "__main__":
    encrypt_onion_address()
