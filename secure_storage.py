#!/usr/bin/env python3
"""
Secure Secret Storage System
Handles sensitive files with proper permissions and protection
"""

import os
import stat
import json
import hashlib
import getpass
from pathlib import Path

class SecureStorage:
    def __init__(self, base_dir="."):
        self.base_dir = Path(base_dir)
        self.secrets_dir = self.base_dir / ".secrets"
        self.ensure_secure_directory()
    
    def ensure_secure_directory(self):
        """Create and secure the secrets directory"""
        try:
            # Create secrets directory if it doesn't exist
            self.secrets_dir.mkdir(mode=0o700, exist_ok=True)
            
            # Ensure proper permissions (owner read/write/execute only)
            os.chmod(self.secrets_dir, 0o700)
            
            print(f"✅ Secure directory: {self.secrets_dir} (permissions: 700)")
            
        except Exception as e:
            print(f"❌ Failed to create secure directory: {e}")
            raise
    
    def store_secret_file(self, filename, content, is_binary=False):
        """Store a secret file with secure permissions"""
        try:
            secret_path = self.secrets_dir / filename
            
            # Write file with secure permissions
            mode = 'wb' if is_binary else 'w'
            with open(secret_path, mode) as f:
                f.write(content)
            
            # Set strict permissions (owner read/write only)
            os.chmod(secret_path, 0o600)
            
            print(f"✅ Stored secret: {filename} (permissions: 600)")
            return secret_path
            
        except Exception as e:
            print(f"❌ Failed to store secret {filename}: {e}")
            raise
    
    def load_secret_file(self, filename, is_binary=False):
        """Load a secret file with permission verification"""
        try:
            secret_path = self.secrets_dir / filename
            
            if not secret_path.exists():
                raise FileNotFoundError(f"Secret file not found: {filename}")
            
            # Verify file permissions
            file_stat = secret_path.stat()
            file_mode = stat.filemode(file_stat.st_mode)
            
            # Check if file is readable by others (security risk)
            if file_stat.st_mode & (stat.S_IRGRP | stat.S_IROTH):
                print(f"⚠️  WARNING: {filename} has insecure permissions: {file_mode}")
                print("⚠️  Fixing permissions...")
                os.chmod(secret_path, 0o600)
            
            # Load file content
            mode = 'rb' if is_binary else 'r'
            with open(secret_path, mode) as f:
                content = f.read()
            
            print(f"✅ Loaded secret: {filename}")
            return content
            
        except Exception as e:
            print(f"❌ Failed to load secret {filename}: {e}")
            raise
    
    def migrate_existing_secrets(self):
        """Migrate existing secret files to secure storage"""
        print("🔄 Migrating existing secrets to secure storage...")
        
        # Files to migrate
        secret_files = [
            ('server.key', True),      # TLS private key (binary)
            ('server.crt', True),      # TLS certificate (binary) 
            ('dh_params.json', False), # DH parameters (text)
        ]
        
        migrated = 0
        for filename, is_binary in secret_files:
            old_path = self.base_dir / filename
            
            if old_path.exists():
                try:
                    # Read existing file
                    mode = 'rb' if is_binary else 'r'
                    with open(old_path, mode) as f:
                        content = f.read()
                    
                    # Store in secure location
                    self.store_secret_file(filename, content, is_binary)
                    
                    # Remove old file
                    old_path.unlink()
                    print(f"✅ Migrated: {filename}")
                    migrated += 1
                    
                except Exception as e:
                    print(f"❌ Failed to migrate {filename}: {e}")
        
        print(f"🎉 Migration complete: {migrated} files migrated")
        return migrated
    
    def get_secret_path(self, filename):
        """Get the secure path for a secret file"""
        return self.secrets_dir / filename
    
    def list_secrets(self):
        """List all stored secrets (for debugging)"""
        try:
            if not self.secrets_dir.exists():
                print("No secrets directory found")
                return []
            
            secrets = []
            for secret_file in self.secrets_dir.iterdir():
                if secret_file.is_file():
                    file_stat = secret_file.stat()
                    file_mode = stat.filemode(file_stat.st_mode)
                    secrets.append({
                        'name': secret_file.name,
                        'permissions': file_mode,
                        'size': file_stat.st_size
                    })
            
            return secrets
            
        except Exception as e:
            print(f"❌ Failed to list secrets: {e}")
            return []

def main():
    """Test the secure storage system"""
    print("🔒 Testing Secure Storage System")
    print("=" * 50)
    
    storage = SecureStorage()
    
    # Migrate existing secrets
    storage.migrate_existing_secrets()
    
    # List current secrets
    secrets = storage.list_secrets()
    if secrets:
        print("\n📋 Current secrets:")
        for secret in secrets:
            print(f"  {secret['name']}: {secret['permissions']} ({secret['size']} bytes)")
    else:
        print("\n📋 No secrets found")

if __name__ == "__main__":
    main()
