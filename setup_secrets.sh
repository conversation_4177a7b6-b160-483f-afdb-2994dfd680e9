#!/bin/bash

# Checkpoint 8: Environment Variables Setup Script for C2 Security
# This script helps externalize static secrets from the source code

echo "🔐 Elissa's Fun House C2 - Security Configuration Setup"
echo "======================================================"
echo ""

# Check if running with source command
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    echo "⚠️  WARNING: This script should be sourced, not executed directly!"
    echo "   Usage: source setup_secrets.sh"
    echo "   Or:    . setup_secrets.sh"
    echo ""
    echo "   This ensures environment variables are set in your current shell."
    exit 1
fi

echo "🎯 Setting up externalized secrets for enhanced security..."
echo ""

# Generate random secrets if not already set
if [[ -z "$C2_PORT_SEED" ]]; then
    # Generate a random port seed
    C2_PORT_SEED="ElissaPortSeed_$(openssl rand -hex 16)_$(date +%s)"
    echo "🔀 Generated new port seed"
else
    echo "🔀 Using existing port seed"
fi

if [[ -z "$C2_MASTER_PASSWORD" ]]; then
    # Generate a random master password
    C2_MASTER_PASSWORD="ElissaMaster_$(openssl rand -base64 32)_SecureKey"
    echo "🔑 Generated new master password"
else
    echo "🔑 Using existing master password"
fi

if [[ -z "$C2_MASTER_SALT" ]]; then
    # Generate a random master salt
    C2_MASTER_SALT="ElissaSalt_$(openssl rand -hex 16)"
    echo "🧂 Generated new master salt"
else
    echo "🧂 Using existing master salt"
fi

# Checkpoint 9: Dynamic traffic obfuscation lists
if [[ -z "$C2_USER_AGENTS" ]]; then
    # Generate custom user agent list (optional)
    echo "🌐 Using default user agent list (can be customized with C2_USER_AGENTS)"
fi

if [[ -z "$C2_ENDPOINTS" ]]; then
    # Generate custom endpoint list (optional)
    echo "🔗 Using default endpoint list (can be customized with C2_ENDPOINTS)"
fi

if [[ -z "$C2_DOMAINS" ]]; then
    # Generate custom domain list (optional)
    echo "🌍 Using default domain list (can be customized with C2_DOMAINS)"
fi

# Export the environment variables
export C2_PORT_SEED
export C2_MASTER_PASSWORD
export C2_MASTER_SALT
export C2_USER_AGENTS
export C2_ENDPOINTS
export C2_DOMAINS

echo ""
echo "✅ Environment variables configured successfully!"
echo ""
echo "📋 Current Configuration:"
echo "   C2_PORT_SEED: ${C2_PORT_SEED:0:20}... (truncated for security)"
echo "   C2_MASTER_PASSWORD: ${C2_MASTER_PASSWORD:0:15}... (truncated for security)"
echo "   C2_MASTER_SALT: ${C2_MASTER_SALT:0:15}... (truncated for security)"
echo ""

# Save to .env file for persistence (optional)
read -p "💾 Save configuration to .env file for persistence? (y/N): " save_env

if [[ "$save_env" =~ ^[Yy]$ ]]; then
    cat > .env << EOF
# Elissa's Fun House C2 - Externalized Secrets
# Generated on: $(date)
#
# SECURITY WARNING: Keep this file secure and never commit to version control!
# Add .env to your .gitignore file

# Core secrets
C2_PORT_SEED="$C2_PORT_SEED"
C2_MASTER_PASSWORD="$C2_MASTER_PASSWORD"
C2_MASTER_SALT="$C2_MASTER_SALT"

# Checkpoint 9: Dynamic traffic obfuscation (optional - leave empty to use defaults)
# C2_USER_AGENTS="Mozilla/5.0 (Custom) Agent 1|Mozilla/5.0 (Custom) Agent 2"
# C2_ENDPOINTS="/custom/api/v1/track|/custom/assets/app.js|/custom/health"
# C2_DOMAINS="custom-cdn.example.com|assets.example.com|api.example.com"
EOF
    
    echo "💾 Configuration saved to .env file"
    echo "⚠️  SECURITY: Add '.env' to your .gitignore file!"
    echo ""
    
    # Check if .gitignore exists and add .env if not present
    if [[ -f .gitignore ]]; then
        if ! grep -q "^\.env$" .gitignore; then
            echo ".env" >> .gitignore
            echo "✅ Added .env to .gitignore"
        else
            echo "✅ .env already in .gitignore"
        fi
    else
        echo ".env" > .gitignore
        echo "✅ Created .gitignore with .env entry"
    fi
fi

echo ""
echo "🚀 Ready to launch C2 server and client with externalized secrets!"
echo ""
echo "📖 Usage Examples:"
echo "   Server: python3 c2_server.py 8080"
echo "   Client: python3 client.py 127.0.0.1 8080"
echo ""
echo "🔒 Security Benefits:"
echo "   ✓ Secrets no longer hardcoded in source"
echo "   ✓ Different secrets per deployment"
echo "   ✓ Environment-based configuration"
echo "   ✓ Reduced risk if source code is discovered"
echo ""
