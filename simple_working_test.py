#!/usr/bin/env python3
"""
Simple working test for the C2 system with proper port synchronization
"""

import subprocess
import time
import sys
import os
import signal

def run_test():
    print("🧪 Running Simple C2 Working Test")
    print("=" * 50)
    
    server_process = None
    client_process = None
    
    try:
        # Start server with standard ports (no dynamic port calculation)
        print("🚀 Starting C2 Server on port 8080...")
        server_process = subprocess.Popen([
            sys.executable, "c2_server.py", "8080", "--standard-ports", "--verbose"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # Give server time to start
        print("⏳ Waiting for server to start...")
        time.sleep(8)
        
        # Check if server is running
        if server_process.poll() is not None:
            stdout, stderr = server_process.communicate()
            print("❌ Server failed to start:")
            print(stdout)
            return False
        
        print("✅ Server started successfully")
        
        # Start client with standard port configuration
        print("🚀 Starting C2 Client...")
        client_process = subprocess.Popen([
            sys.executable, "client.py", "127.0.0.1", "--standard-ports"
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # Monitor client for 30 seconds
        print("⏳ Monitoring client connection for 30 seconds...")
        
        start_time = time.time()
        client_output = []
        server_output = []
        
        while time.time() - start_time < 30:
            # Read client output
            if client_process.poll() is None:
                try:
                    line = client_process.stdout.readline()
                    if line:
                        client_output.append(line.strip())
                        print(f"[CLIENT] {line.strip()}")
                except:
                    pass
            
            # Read server output
            if server_process.poll() is None:
                try:
                    line = server_process.stdout.readline()
                    if line:
                        server_output.append(line.strip())
                        print(f"[SERVER] {line.strip()}")
                except:
                    pass
            
            time.sleep(0.1)
        
        # Analyze results
        print("\n" + "=" * 50)
        print("🧪 Test Results Analysis")
        print("=" * 50)
        
        client_text = "\n".join(client_output)
        server_text = "\n".join(server_output)
        combined_text = client_text + "\n" + server_text
        
        # Check for successful connection indicators
        success_indicators = [
            ("Server Started", "Server started" in server_text.lower()),
            ("Bootstrap Server", "bootstrap server" in server_text.lower()),
            ("Client Connected", "client connected" in combined_text.lower() or "tls handshake completed" in combined_text.lower()),
            ("TLS Handshake", "tls handshake" in combined_text.lower() or "mutual tls" in combined_text.lower()),
            ("Certificate Exchange", "certificate" in combined_text.lower()),
            ("Secure Communication", "application layer security" in combined_text.lower() or "session key" in combined_text.lower())
        ]
        
        passed = 0
        total = len(success_indicators)
        
        for test_name, result in success_indicators:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status}: {test_name}")
            if result:
                passed += 1
        
        print("=" * 50)
        print(f"Results: {passed}/{total} tests passed")
        
        if passed >= total * 0.7:  # 70% pass rate
            print("🎉 TEST SUCCESSFUL - Core functionality working!")
            return True
        else:
            print("💥 TEST FAILED - Issues detected")
            print("\nClient Output:")
            print("\n".join(client_output[-10:]))  # Last 10 lines
            print("\nServer Output:")
            print("\n".join(server_output[-10:]))  # Last 10 lines
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
        
    finally:
        # Cleanup
        print("\n🧹 Cleaning up...")
        
        if client_process:
            try:
                client_process.terminate()
                client_process.wait(timeout=5)
            except:
                client_process.kill()
        
        if server_process:
            try:
                server_process.terminate()
                server_process.wait(timeout=5)
            except:
                server_process.kill()
        
        print("✅ Cleanup complete")

if __name__ == "__main__":
    success = run_test()
    sys.exit(0 if success else 1)
