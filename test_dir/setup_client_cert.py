#!/usr/bin/env python3
"""
Setup client certificate for C2 client
This creates a client certificate that the server will authorize
"""

import sys
from c2_server import TLSCertificateManager

def main():
    print("🔐 Setting up client certificate for C2 connection...")
    
    # Initialize TLS manager
    tls_manager = TLSCertificateManager()
    
    # Generate client certificate
    client_id = "production-client-001"
    success, client_cert, client_key = tls_manager.generate_client_certificate(client_id)
    
    if success:
        # Store client certificate for client to use
        tls_manager.secure_storage.store_secret_file("client.crt", client_cert, is_binary=True)
        tls_manager.secure_storage.store_secret_file("client.key", client_key, is_binary=True)
        
        print(f"✅ Client certificate generated and stored")
        print(f"🆔 Client ID: {client_id}")
        print(f"📁 Certificate stored in secure storage")
        print(f"🔗 Client can now connect to server with mutual TLS")
        return True
    else:
        print(f"❌ Failed to generate client certificate")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
