#!/usr/bin/env python3
"""
Test script for padding and metadata minimization
Tests fixed-size frames, padding removal, and HTTP randomization
"""

import sys
import time
import json
import base64
import os
import random
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class TestSecureCrypto:
    def __init__(self):
        # Same key generation as server/client
        password = b"ElissasFunHouse2024SecureKey!@#$%^&*()"
        salt = b"SecureSalt123456"

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password))
        self.cipher = Fernet(key)
        
        # Replay protection - track used nonces and timestamps
        self.used_nonces = set()
        self.max_nonce_age = 300  # 5 minutes
        self.last_cleanup = time.time()
        
        # Padding configuration for metadata minimization
        self.fixed_frame_sizes = [1024, 2048, 4096, 8192]  # Standard frame sizes
        self.padding_char = b'\x00'  # Null byte padding

    def encrypt(self, data):
        """Encrypt data with replay protection and padding"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        # Add timestamp and nonce for replay protection
        timestamp = int(time.time())
        nonce = os.urandom(16).hex()  # 32-char hex string
        
        # Create message with metadata
        message = {
            'timestamp': timestamp,
            'nonce': nonce,
            'data': base64.b64encode(data).decode('ascii')
        }
        
        message_json = json.dumps(message)
        message_bytes = message_json.encode('utf-8')
        
        # Apply padding to hide actual message length
        padded_message = self._add_padding(message_bytes)
        
        # Encrypt the padded message
        encrypted = self.cipher.encrypt(padded_message)
        
        # Clear sensitive data from memory
        message_json = None
        message_bytes = None
        padded_message = None
        del message_json, message_bytes, padded_message
        
        return encrypted

    def decrypt(self, encrypted_data):
        """Decrypt data with replay protection validation and padding removal"""
        try:
            # Decrypt the message
            decrypted_padded = self.cipher.decrypt(encrypted_data)
            
            # Remove padding
            decrypted_bytes = self._remove_padding(decrypted_padded)
            decrypted_json = decrypted_bytes.decode('utf-8')
            message = json.loads(decrypted_json)
            
            # Clear decrypted JSON from memory immediately
            decrypted_json = None
            del decrypted_json
            
            # Validate message structure
            if not all(key in message for key in ['timestamp', 'nonce', 'data']):
                raise ValueError("Invalid message structure")
            
            timestamp = message['timestamp']
            nonce = message['nonce']
            data_b64 = message['data']
            
            # Validate timestamp (reject messages older than 5 minutes)
            current_time = int(time.time())
            if current_time - timestamp > self.max_nonce_age:
                raise ValueError("Message too old - possible replay attack")
            
            # Validate nonce (prevent replay attacks)
            if nonce in self.used_nonces:
                raise ValueError("Nonce already used - replay attack detected")
            
            # Add nonce to used set
            self.used_nonces.add(nonce)
            
            # Periodic cleanup of old nonces
            if current_time - self.last_cleanup > 60:  # Cleanup every minute
                self._cleanup_old_nonces(current_time)
                self.last_cleanup = current_time
            
            # Decode and return the actual data
            actual_data = base64.b64decode(data_b64.encode('ascii')).decode('utf-8')
            
            # Clear sensitive data from memory
            message = None
            data_b64 = None
            del message, data_b64
            
            return actual_data
            
        except Exception as e:
            # Clear any sensitive data that might be in memory
            try:
                decrypted_json = None
                message = None
                del decrypted_json, message
            except:
                pass
            raise ValueError(f"Decryption/validation failed: {str(e)}")
    
    def _cleanup_old_nonces(self, current_time):
        """Remove old nonces to prevent memory bloat"""
        if len(self.used_nonces) > 10000:  # Prevent memory bloat
            self.used_nonces.clear()
    
    def _add_padding(self, data):
        """Add padding to hide actual message length"""
        data_len = len(data)
        
        # Find the smallest frame size that can fit the data
        target_size = None
        for size in self.fixed_frame_sizes:
            if data_len <= size:
                target_size = size
                break
        
        # If data is larger than largest frame, use next power of 2
        if target_size is None:
            target_size = 1
            while target_size < data_len:
                target_size *= 2
        
        # Calculate padding needed
        padding_needed = target_size - data_len
        
        # Add random padding followed by length indicator
        if padding_needed > 4:  # Need at least 4 bytes for length
            random_padding = os.urandom(padding_needed - 4)
            length_bytes = data_len.to_bytes(4, byteorder='big')
            padded_data = data + random_padding + length_bytes
        else:
            # If not enough space for length indicator, just pad with nulls
            padded_data = data + (self.padding_char * padding_needed)
        
        return padded_data
    
    def _remove_padding(self, padded_data):
        """Remove padding to recover original message"""
        if len(padded_data) < 4:
            return padded_data  # Too small to have length indicator
        
        # Try to extract length from last 4 bytes
        try:
            length_bytes = padded_data[-4:]
            original_length = int.from_bytes(length_bytes, byteorder='big')
            
            # Validate length makes sense
            if 0 < original_length <= len(padded_data) - 4:
                return padded_data[:original_length]
        except:
            pass
        
        # Fallback: remove null byte padding from end
        return padded_data.rstrip(self.padding_char)

def test_padding_consistency():
    """Test that padding produces consistent frame sizes"""
    print("📏 Testing padding consistency...")
    crypto = TestSecureCrypto()
    
    test_messages = [
        "short",
        "medium length message",
        "this is a much longer message that should test the padding system thoroughly",
        "x" * 500,  # Medium message
        "y" * 2000,  # Large message
    ]
    
    encrypted_sizes = []
    for msg in test_messages:
        encrypted = crypto.encrypt(msg)
        encrypted_sizes.append(len(encrypted))
        
        # Verify we can decrypt it back
        decrypted = crypto.decrypt(encrypted)
        assert decrypted == msg, f"Decryption failed for: {msg[:20]}..."
    
    print(f"✅ Encrypted sizes: {encrypted_sizes}")
    
    # Check that similar-sized messages have similar encrypted sizes
    short_msgs = ["a", "ab", "abc", "abcd"]
    short_sizes = [len(crypto.encrypt(msg)) for msg in short_msgs]
    
    # All short messages should have similar encrypted sizes due to padding
    size_variance = max(short_sizes) - min(short_sizes)
    print(f"✅ Short message size variance: {size_variance} bytes")
    
    print("✅ Padding consistency test passed")

def test_metadata_minimization():
    """Test that metadata is minimized in encrypted payloads"""
    print("🔍 Testing metadata minimization...")
    crypto = TestSecureCrypto()
    
    # Test that different message lengths result in similar encrypted sizes
    messages = {
        "tiny": "hi",
        "small": "hello world",
        "medium": "this is a medium sized message for testing",
        "large": "this is a much larger message that contains significantly more content to test the padding system"
    }
    
    results = {}
    for name, msg in messages.items():
        encrypted = crypto.encrypt(msg)
        results[name] = {
            'original_size': len(msg),
            'encrypted_size': len(encrypted)
        }
    
    print("📊 Message size analysis:")
    for name, data in results.items():
        ratio = data['encrypted_size'] / data['original_size']
        print(f"  {name:8}: {data['original_size']:3d} -> {data['encrypted_size']:4d} bytes (ratio: {ratio:.1f}x)")
    
    print("✅ Metadata minimization test completed")

def test_frame_size_distribution():
    """Test that messages are distributed across frame sizes appropriately"""
    print("📦 Testing frame size distribution...")
    crypto = TestSecureCrypto()
    
    frame_counts = {1024: 0, 2048: 0, 4096: 0, 8192: 0, 'larger': 0}
    
    # Test various message sizes
    for size in range(1, 1000, 50):  # Test messages from 1 to 1000 chars
        msg = "x" * size
        encrypted = crypto.encrypt(msg)
        encrypted_size = len(encrypted)
        
        # Determine which frame this would fit in
        if encrypted_size <= 1024:
            frame_counts[1024] += 1
        elif encrypted_size <= 2048:
            frame_counts[2048] += 1
        elif encrypted_size <= 4096:
            frame_counts[4096] += 1
        elif encrypted_size <= 8192:
            frame_counts[8192] += 1
        else:
            frame_counts['larger'] += 1
    
    print("📊 Frame size distribution:")
    for frame_size, count in frame_counts.items():
        print(f"  {frame_size:>6}: {count:3d} messages")
    
    print("✅ Frame size distribution test completed")

def run_all_tests():
    """Run all padding and metadata minimization tests"""
    print("🧪 Starting padding and metadata minimization tests...\n")
    
    tests = [
        test_padding_consistency,
        test_metadata_minimization,
        test_frame_size_distribution
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            test()
            passed += 1
        except Exception as e:
            print(f"❌ {test.__name__} failed: {e}")
            failed += 1
        print()
    
    print("=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All padding and metadata tests passed!")
        return True
    else:
        print("💥 Some tests failed!")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
