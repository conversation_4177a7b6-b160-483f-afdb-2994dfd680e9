#!/usr/bin/env python3
"""
Test script for secure TLS implementation
Tests the enhanced TLS mutual authentication with no insecure fallbacks
"""

import sys
import time
import threading
import subprocess
import socket
import ssl
import json
from pathlib import Path

def test_secure_bootstrap():
    """Test the secure bootstrap process"""
    print("🧪 Testing secure TLS bootstrap process...")
    
    # Start the C2 server in background
    print("🚀 Starting C2 server...")
    server_process = subprocess.Popen([
        sys.executable, "c2_server.py", "8080", "--verbose"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    
    # Give server time to start
    time.sleep(3)
    
    try:
        # Test secure bootstrap connection
        print("🔗 Testing secure bootstrap connection...")
        
        # Connect to bootstrap port (8080 + 1000 = 9080)
        bootstrap_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        bootstrap_socket.settimeout(10.0)
        
        try:
            bootstrap_socket.connect(("localhost", 9080))
            print("✅ Connected to bootstrap server")
            
            # Create SSL context for testing
            ssl_context = ssl.create_default_context()
            ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
            ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE  # For testing
            
            # Wrap with TLS
            tls_socket = ssl_context.wrap_socket(bootstrap_socket, server_hostname="localhost")
            print("✅ TLS handshake completed")
            
            # Test CA certificate request
            request = {
                'action': 'get_ca_cert',
                'client_type': 'c2_client'
            }
            request_json = json.dumps(request).encode()
            tls_socket.send(len(request_json).to_bytes(4, 'big') + request_json)
            
            # Receive response
            response_len = int.from_bytes(tls_socket.recv(4), 'big')
            response_data = tls_socket.recv(response_len)
            response = json.loads(response_data.decode())
            
            if response.get('status') == 'success':
                print("✅ CA certificate received successfully")
                ca_cert = response.get('ca_cert')
                if ca_cert and '-----BEGIN CERTIFICATE-----' in ca_cert:
                    print("✅ CA certificate format is valid")
                else:
                    print("❌ CA certificate format is invalid")
            else:
                print(f"❌ CA certificate request failed: {response.get('error')}")
            
            tls_socket.close()
            
        except Exception as e:
            print(f"❌ Bootstrap connection failed: {e}")
            
    finally:
        # Clean up server process
        server_process.terminate()
        try:
            server_process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            server_process.kill()
        print("🛑 Server stopped")

def test_certificate_loading():
    """Test in-memory certificate loading"""
    print("\n🧪 Testing in-memory certificate loading...")
    
    # Test certificate validation
    test_cert = """-----BEGIN CERTIFICATE-----
MIICljCCAX4CCQCKVjJzQqGdOzANBgkqhkiG9w0BAQsFADANMQswCQYDVQQGEwJV
UzAeFw0yNTAxMTUwMDAwMDBaFw0yNjAxMTUwMDAwMDBaMBUxEzARBgNVBAMMCnRl
c3QtY2xpZW50MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890
-----END CERTIFICATE-----"""
    
    test_key = """-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDXNjc4OTAwMTIz
-----END PRIVATE KEY-----"""
    
    try:
        from cryptography import x509
        from cryptography.hazmat.primitives import serialization
        
        # This should fail with invalid certificate, which is expected
        try:
            cert_obj = x509.load_pem_x509_certificate(test_cert.encode())
            print("❌ Invalid certificate was accepted (this shouldn't happen)")
        except Exception:
            print("✅ Invalid certificate properly rejected")
            
        print("✅ Certificate validation working correctly")
        
    except ImportError:
        print("⚠️ Cryptography library not available for testing")

def test_security_features():
    """Test security features"""
    print("\n🧪 Testing security features...")
    
    # Test 1: Verify no insecure fallbacks
    print("🔒 Testing no insecure fallbacks...")
    
    # Import client module to test
    try:
        import client
        
        # Create a test client instance
        test_client = client.C2Client()
        
        # Test that client requires CA certificate
        if hasattr(test_client, 'embedded_ca_cert'):
            test_client.embedded_ca_cert = None
            print("✅ Client properly requires CA certificate")
        else:
            print("⚠️ Could not test CA certificate requirement")
            
    except ImportError as e:
        print(f"⚠️ Could not import client module: {e}")
    
    # Test 2: Verify TLS 1.3 enforcement
    print("🔒 Testing TLS 1.3 enforcement...")
    
    try:
        ssl_context = ssl.create_default_context()
        ssl_context.minimum_version = ssl.TLSVersion.TLSv1_3
        ssl_context.maximum_version = ssl.TLSVersion.TLSv1_3
        
        if ssl_context.minimum_version == ssl.TLSVersion.TLSv1_3:
            print("✅ TLS 1.3 enforcement working")
        else:
            print("❌ TLS 1.3 enforcement failed")
            
    except Exception as e:
        print(f"❌ TLS version test failed: {e}")

def main():
    """Run all tests"""
    print("🧪 Starting Secure TLS Implementation Tests")
    print("=" * 50)
    
    # Test 1: Secure bootstrap
    test_secure_bootstrap()
    
    # Test 2: Certificate loading
    test_certificate_loading()
    
    # Test 3: Security features
    test_security_features()
    
    print("\n" + "=" * 50)
    print("🧪 Secure TLS Tests Completed")

if __name__ == "__main__":
    main()
